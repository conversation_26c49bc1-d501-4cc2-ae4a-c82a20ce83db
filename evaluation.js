// إعداد متغيرات عامة - استخدام config.js
const API_URL = window.CONFIG ? window.CONFIG.API_URL : (localStorage.getItem('serverUrl') || 'http://localhost:5500/api');
let employees = [];
let evaluations = [];

// تحميل البيانات عند تحميل الصفحة
window.addEventListener('DOMContentLoaded', function() {
  loadEmployees();
  loadEvaluations();
  setupEventListeners();
  setupModalClosers();
  setupReportEventListeners();
  setupTopBottomEventListeners();
  setupUnevaluatedEventListeners();
  setupEvaluationFilters();

  // تهيئة التقارير
  loadReportDepartments();
  fetchAllEvaluationsForReport();

  // تهيئة DataTables للتقييمات بعد تأخير قصير للتأكد من تحميل jQuery
  setTimeout(() => {
    initializeEvaluationDataTables();
    setupAdvancedEvaluationSearch();
  }, 1000);

  // التحقق من المحتوى المحدد بعد تحميل الصفحة
  setTimeout(() => {
    checkSelectedContent();
  }, 100);
});

// إعداد مستمعي الأحداث لإغلاق النوافذ المنبثقة
function setupModalClosers() {
  // إغلاق نافذة تعديل التقييم
  document.getElementById('closeEditEvaluationModal').addEventListener('click', function() {
    document.getElementById('editEvaluationModal').style.display = 'none';
  });
  
  // إغلاق النافذة المنبثقة عند النقر خارجها
  window.addEventListener('click', function(event) {
    const evaluationModal = document.getElementById('editEvaluationModal');
    
    if (event.target === evaluationModal) {
      evaluationModal.style.display = 'none';
    }
  });
}

// إعداد مستمعي الأحداث للتقارير
function setupReportEventListeners() {
  // فلاتر التقارير العامة
  document.getElementById('applyReportFilters')?.addEventListener('click', applyReportFiltersFunc);
  document.getElementById('resetReportFilters')?.addEventListener('click', resetReportFiltersFunc);
  document.getElementById('exportReportTableBtn')?.addEventListener('click', exportReportTable);

  // البحث في جدول التقارير
  document.getElementById('searchReportTable')?.addEventListener('input', function() {
    searchReportTableFunc();
  });
}

// إعداد مستمعي الأحداث لتبويب أعلى وأقل التقييمات
function setupTopBottomEventListeners() {
  document.getElementById('applyTopBottomFilters')?.addEventListener('click', applyTopBottomFilters);
  document.getElementById('resetTopBottomFilters')?.addEventListener('click', resetTopBottomFilters);
  document.getElementById('exportTopBottomBtn')?.addEventListener('click', exportTopBottomTable);
  
  // البحث في جدول أعلى وأقل التقييمات
  document.getElementById('searchTopBottomTable')?.addEventListener('input', function() {
    searchInTopBottomTable(this.value);
  });
}

// إعداد مستمعي الأحداث لتبويب الموظفين غير المقيمين
function setupUnevaluatedEventListeners() {
  document.getElementById('applyUnevaluatedFilters')?.addEventListener('click', applyUnevaluatedFilters);
  document.getElementById('resetUnevaluatedFilters')?.addEventListener('click', resetUnevaluatedFilters);
  document.getElementById('exportUnevaluatedBtn')?.addEventListener('click', exportUnevaluatedTable);
  
  // إظهار/إخفاء حقول الفترة المخصصة
  document.getElementById('unevaluatedPeriod')?.addEventListener('change', function() {
    const customGroups = document.querySelectorAll('#customPeriodGroup, #customPeriodGroupEnd');
    if (this.value === 'custom') {
      customGroups.forEach(group => group.style.display = 'block');
    } else {
      customGroups.forEach(group => group.style.display = 'none');
    }
  });
  
  // البحث في جدول الموظفين غير المقيمين
  document.getElementById('searchUnevaluatedTable')?.addEventListener('input', function() {
    searchInUnevaluatedTable(this.value);
  });
}

function setupEventListeners() {
  // البحث الذكي عن الموظف
  const employeeSearchInput = document.getElementById('evaluationEmployeeSearch');
  if (employeeSearchInput) {
    employeeSearchInput.addEventListener('input', handleEmployeeSearch);
    employeeSearchInput.addEventListener('change', handleEmployeeSelection);
  }

  // البحث في التقييمات من السيرفر
  const searchEvaluationInput = document.getElementById('searchEvaluation');
  if (searchEvaluationInput) {
    searchEvaluationInput.addEventListener('input', function() {
      clearTimeout(this.searchTimeout);
      this.searchTimeout = setTimeout(() => {
        searchEvaluationsFromServer();
      }, 300);
    });
  }

  // حفظ أو تعديل التقييم
  const saveEvaluationBtn = document.getElementById('saveEvaluation');
  if (saveEvaluationBtn) {
    saveEvaluationBtn.addEventListener('click', async function(e) {
      e.preventDefault();

      // تحويل التواريخ إلى الصيغة الصحيحة (تجنب مشاكل المنطقة الزمنية)
      const startDateValue = document.getElementById('evaluationStartDate').value;
      const endDateValue = document.getElementById('evaluationEndDate').value;

      function formatDateSafely(dateString) {
        if (!dateString) return null;
        // استخدام التاريخ مباشرة بدون تحويل لتجنب مشاكل المنطقة الزمنية
        return dateString;
      }

      const data = {
        employee_code: document.getElementById('evaluationEmployeeCode').value,
        employee_name: document.getElementById('evaluationEmployeeName').value,
        department: document.getElementById('evaluationEmployeeDepartment').value,
        evaluation_type: document.getElementById('evaluationType').value,
        evaluation_period: document.getElementById('evaluationType').value,
        start_date: formatDateSafely(startDateValue),
        end_date: formatDateSafely(endDateValue),
        evaluation_date: formatDateSafely(startDateValue), // الخادم يتوقع هذا الحقل
        overall_score: document.getElementById('evaluationScore').value, // الخادم يتوقع overall_score
        score: document.getElementById('evaluationScore').value, // للتوافق مع الكود الحالي
        notes: document.getElementById('evaluationNotes').value,
        comments: document.getElementById('evaluationNotes').value // الخادم يتوقع comments
      };

      console.log('📅 بيانات التقييم مع التواريخ المحولة:', data);
      if (!data.employee_code || !data.employee_name || !data.department || !data.evaluation_type || !data.start_date || !data.end_date || !data.score) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
      }
      const editId = saveEvaluationBtn.dataset.editId;

      // تعطيل الزر أثناء الحفظ
      saveEvaluationBtn.disabled = true;
      const originalText = saveEvaluationBtn.textContent;
      saveEvaluationBtn.textContent = editId ? 'جاري التحديث...' : 'جاري الحفظ...';

      try {
        let response;
        const token = localStorage.getItem('token');
        if (editId) {
          response = await fetch(`${API_URL}/evaluations/${editId}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(data)
          });
        } else {
          response = await fetch(`${API_URL}/evaluations`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(data)
          });
        }
        if (response.ok) {
          // عرض رسالة نجاح
          alert(editId ? 'تم تحديث التقييم بنجاح' : 'تم حفظ التقييم بنجاح');

          // تحديث الجدول
          displayEvaluationsWithDataTables();

          // إعادة تعيين النموذج
          resetEvaluationForm();
          delete saveEvaluationBtn.dataset.editId;
        } else {
          alert(editId ? 'فشل في تحديث التقييم' : 'فشل في حفظ التقييم');
        }
      } catch (error) {
        console.error('خطأ في حفظ/تحديث التقييم:', error);
        alert(editId ? 'حدث خطأ أثناء تحديث التقييم. يرجى المحاولة مرة أخرى.' : 'حدث خطأ أثناء حفظ التقييم. يرجى المحاولة مرة أخرى.');
      } finally {
        // إعادة تفعيل الزر
        saveEvaluationBtn.disabled = false;
        saveEvaluationBtn.textContent = originalText;
      }
    });
  }

  // إعادة تعيين النموذج
  const resetEvaluationBtn = document.getElementById('resetEvaluationForm');
  if (resetEvaluationBtn) {
    resetEvaluationBtn.addEventListener('click', function(e) {
      e.preventDefault();
      resetEvaluationForm();
    });
  }

  // تصدير إلى Excel
  const exportEvaluationsBtn = document.getElementById('exportEvaluationsBtn');
  if (exportEvaluationsBtn) {
    exportEvaluationsBtn.addEventListener('click', function() {
      exportEvaluationsToExcel();
    });
  }

  // تحديث التقييم من النافذة المنبثقة
  const updateEvaluationBtn = document.getElementById('updateEvaluationBtn');
  if (updateEvaluationBtn) {
    updateEvaluationBtn.addEventListener('click', async function(e) {
      e.preventDefault();

      // تحويل التواريخ إلى الصيغة الصحيحة (تجنب مشاكل المنطقة الزمنية)
      const startDateValue = document.getElementById('editEvaluationStartDate').value;
      const endDateValue = document.getElementById('editEvaluationEndDate').value;

      function formatDateSafely(dateString) {
        if (!dateString) return null;
        // استخدام التاريخ مباشرة بدون تحويل لتجنب مشاكل المنطقة الزمنية
        return dateString;
      }

      const data = {
        employee_code: document.getElementById('editEvaluationEmployeeCode').value,
        employee_name: document.getElementById('editEvaluationEmployeeName').value,
        department: document.getElementById('editEvaluationEmployeeDepartment').value,
        evaluation_type: document.getElementById('editEvaluationType').value,
        evaluation_period: document.getElementById('editEvaluationType').value,
        start_date: formatDateSafely(startDateValue),
        end_date: formatDateSafely(endDateValue),
        evaluation_date: formatDateSafely(startDateValue), // الخادم يتوقع هذا الحقل
        overall_score: document.getElementById('editEvaluationScore').value, // الخادم يتوقع overall_score
        score: document.getElementById('editEvaluationScore').value, // للتوافق مع الكود الحالي
        notes: document.getElementById('editEvaluationNotes').value,
        comments: document.getElementById('editEvaluationNotes').value // الخادم يتوقع comments
      };

      console.log('📅 بيانات تحديث التقييم مع التواريخ المحولة:', data);
      
      if (!data.employee_code || !data.employee_name || !data.department || !data.evaluation_type || !data.start_date || !data.end_date || !data.score) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
      }
      
      const editId = updateEvaluationBtn.dataset.editId;
      if (!editId) {
        alert('خطأ: لم يتم العثور على معرف التقييم');
        return;
      }

      // تعطيل الزر أثناء التحديث
      updateEvaluationBtn.disabled = true;
      const originalText = updateEvaluationBtn.textContent;
      updateEvaluationBtn.textContent = 'جاري التحديث...';

      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/evaluations/${editId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify(data)
        });
        
        if (response.ok) {
          displayEvaluationsWithDataTables();
          document.getElementById('editEvaluationModal').style.display = 'none';
          alert('تم تحديث التقييم بنجاح');
        } else {
          const errorData = await response.json().catch(() => ({}));
          alert('فشل في تحديث التقييم: ' + (errorData.error || 'خطأ غير معروف'));
        }
      } catch (error) {
        console.error('خطأ في تحديث التقييم:', error);
        alert('حدث خطأ أثناء تحديث التقييم. يرجى المحاولة مرة أخرى.');
      } finally {
        // إعادة تفعيل الزر
        updateEvaluationBtn.disabled = false;
        updateEvaluationBtn.textContent = originalText;
      }
    });
  }

  // Reports Tab Event Listeners
  const reportsTab = document.querySelector('[data-tab="evaluation-reports"]');
  if (reportsTab) {
    reportsTab.addEventListener('click', function() {
      loadReportDepartments();
      fetchAllEvaluationsForReport();
    });
  }



  const exportReportTableBtn = document.getElementById('exportReportTableBtn');
  if (exportReportTableBtn) {
    exportReportTableBtn.addEventListener('click', exportReportTableFunc);
  }
}



async function loadEmployees() {
  try {
    // إضافة معامل لاستبعاد الموظفين المستقيلين
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/employees?include_resigned=false`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (response.ok) {
      employees = await response.json();
      updateEmployeeSearchSuggestions();
    } else {
      employees = [];
    }
  } catch (error) {
    employees = [];
  }
}

function updateEmployeeSearchSuggestions() {
  const datalist = document.getElementById('evaluationEmployeeSearchSuggestions');
  datalist.innerHTML = '';
  employees.forEach(emp => {
    const option = document.createElement('option');
    option.value = `${emp.code} - ${emp.full_name}`;
    datalist.appendChild(option);
  });
}

function handleEmployeeSearch(e) {
  const searchTerm = e.target.value.trim();
  handleEmployeeSearchLogic(searchTerm, 'evaluationEmployeeSearchSuggestions', 'evaluation');
}

function handleEmployeeSelection(e) {
  const value = e.target.value.trim();
  handleEmployeeSelectionLogic(value, 'evaluation');
}

function handleEmployeeSearchLogic(searchTerm, datalistId, prefix) {
  const datalist = document.getElementById(datalistId);
  datalist.innerHTML = '';
  

  
  if (!searchTerm || searchTerm.trim() === '') {
    document.getElementById(`${prefix}EmployeeCode`).value = '';
    document.getElementById(`${prefix}EmployeeName`).value = '';
    document.getElementById(`${prefix}EmployeeDepartment`).value = '';
    return;
  }
  
  const searchTermTrimmed = searchTerm.trim();

  const filteredEmployees = employees.filter(emp => {
    const fullName = emp.full_name ? String(emp.full_name) : '';
    const code = emp.code ? String(emp.code) : '';
    const department = emp.department ? String(emp.department) : '';

    return SharedUtils.enhancedSearch(searchTermTrimmed, fullName) ||
           code.includes(searchTermTrimmed) ||
           SharedUtils.enhancedSearch(searchTermTrimmed, department);
  });
  
  console.log(`نتائج البحث: ${filteredEmployees.length} موظف`);
  if (filteredEmployees.length > 0) {
    console.log('أول نتيجة:', filteredEmployees[0]);
  }
  
  const exactCodeMatch = employees.find(emp =>
    emp.code && String(emp.code) === searchTermTrimmed
  );
  const searchTermLower = searchTermTrimmed.toLowerCase();
  const exactNameMatch = employees.find(emp => 
    emp.full_name && String(emp.full_name).toLowerCase() === searchTermLower
  );
  
  if (exactCodeMatch) {
    fillEmployeeFieldsAdvanced(exactCodeMatch, prefix);
  } else if (exactNameMatch) {
    fillEmployeeFieldsAdvanced(exactNameMatch, prefix);
  }
  else if (filteredEmployees.length === 1) {
    fillEmployeeFieldsAdvanced(filteredEmployees[0], prefix);
  }
  else {
    const startsWithMatch = employees.find(emp => {
      const nameLower = emp.full_name ? String(emp.full_name).toLowerCase() : '';
      const codeLower = emp.code ? String(emp.code).toLowerCase() : '';
      return nameLower.startsWith(searchTermLower) || codeLower.startsWith(searchTermLower);
    });
    
    if (startsWithMatch) {
      fillEmployeeFieldsAdvanced(startsWithMatch, prefix);
    }
  }
  
  if (filteredEmployees.length > 0) {
    filteredEmployees.sort((a, b) => {
      const aCodeMatch = a.code && String(a.code).toLowerCase().includes(searchTermLower);
      const bCodeMatch = b.code && String(b.code).toLowerCase().includes(searchTermLower);
      
      if (aCodeMatch && !bCodeMatch) return -1;
      if (!aCodeMatch && bCodeMatch) return 1;
      
      return (a.full_name || '').localeCompare(b.full_name || '');
    });
    
    filteredEmployees.slice(0, 15).forEach(emp => {
      const option = document.createElement('option');
      option.value = `${emp.code} - ${emp.full_name}`;
      datalist.appendChild(option);
    });
  } else {
    const option = document.createElement('option');
    option.value = "لا توجد نتائج مطابقة";
    datalist.appendChild(option);
  }
}

function fillEmployeeFieldsAdvanced(employee, prefix) {
  if (employee) {
    document.getElementById(`${prefix}EmployeeCode`).value = employee.code || '';
    document.getElementById(`${prefix}EmployeeName`).value = employee.full_name || '';
    document.getElementById(`${prefix}EmployeeDepartment`).value = employee.department || '';
  }
}

function handleEmployeeSelectionLogic(value, prefix) {
  if (!value) return;
  
  const codeMatch = value.match(/^(\S+)\s*-/);
  if (!codeMatch) return;
  
  const code = codeMatch[1];
  const employee = employees.find(emp => emp.code == code);
  
  if (employee) {
    document.getElementById(`${prefix}EmployeeCode`).value = employee.code;
    document.getElementById(`${prefix}EmployeeName`).value = employee.full_name;
    document.getElementById(`${prefix}EmployeeDepartment`).value = employee.department || '';
  }
}

function fillEmployeeFields(emp) {
  document.getElementById('evaluationEmployeeCode').value = emp.code;
  document.getElementById('evaluationEmployeeName').value = emp.full_name;
  document.getElementById('evaluationEmployeeDepartment').value = emp.department;
}

const evaluationTypeInput = document.getElementById('evaluationType');
const evaluationStartDateInput = document.getElementById('evaluationStartDate');
const evaluationEndDateInput = document.getElementById('evaluationEndDate');

function updateEvaluationDates() {
  const type = evaluationTypeInput.value;
  const startDateStr = evaluationStartDateInput.value;
  if (!type || !startDateStr) return;
  const startDate = new Date(startDateStr);
  let endDate;
  if (type === 'شهري') {
    startDate.setDate(1);
    endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0);
  } else if (type === 'ربع سنوي') {
    startDate.setDate(1);
    const quarterEndMonth = startDate.getMonth() + 2;
    endDate = new Date(startDate.getFullYear(), quarterEndMonth + 1, 0);
  }
  evaluationStartDateInput.value = startDate.toISOString().split('T')[0];
  evaluationEndDateInput.value = endDate.toISOString().split('T')[0];
}

async function loadEvaluations() {
  try {
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/evaluations`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (response.ok) {
      evaluations = await response.json();
      // استخدام DataTables بدلاً من العرض التقليدي
      displayEvaluationsWithDataTables();
    } else {
      evaluations = [];
      displayEvaluationsWithDataTables();
    }
  } catch (error) {
    evaluations = [];
    displayEvaluationsWithDataTables();
  }
}

function displayEvaluations() {
  const tableBody = document.querySelector('#evaluation-table tbody');
  tableBody.innerHTML = '';

  // ترتيب البيانات حسب تاريخ الإنشاء (الأحدث أولاً)
  const sortedEvaluations = [...evaluations].sort((a, b) => {
    // ترتيب حسب created_at إذا كان متوفراً، وإلا حسب id
    const aTime = a.created_at ? new Date(a.created_at).getTime() : (a.id || 0);
    const bTime = b.created_at ? new Date(b.created_at).getTime() : (b.id || 0);
    return bTime - aTime; // الأحدث أولاً
  });

  sortedEvaluations.forEach((ev, index) => {
    const tr = document.createElement('tr');

    // تمييز الصف الأول (آخر إضافة) بلون أخضر فاتح
    if (index === 0) {
      tr.style.backgroundColor = '#e8f5e8';
      tr.style.border = '2px solid #4CAF50';
    }

    tr.innerHTML = `
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${ev.employee_code}</td>
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${ev.employee_name}</td>
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${ev.department}</td>
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${ev.evaluation_type}</td>
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${formatDate(ev.start_date)}</td>
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${formatDate(ev.end_date)}</td>
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${ev.score} %</td>
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${ev.notes || ''}</td>
      <td>
        ${hasPermission('edit_evaluation') ? `<button class="edit-btn" onclick="editEvaluation(${ev.id})" title="تعديل التقييم"><i class="fas fa-edit"></i></button>` : ''}
        ${hasPermission('delete_evaluation') ? `<button class="delete-btn" onclick="deleteEvaluation(${ev.id})" title="حذف التقييم"><i class="fas fa-trash-alt"></i></button>` : ''}
      </td>
    `;
    tableBody.appendChild(tr);
  });

  // تطبيق الصلاحيات على الأزرار
  applyEvaluationPermissions();
}

function displayFilteredEvaluations(list) {
  const tableBody = document.querySelector('#evaluation-table tbody');
  tableBody.innerHTML = '';
  list.forEach(ev => {
    const tr = document.createElement('tr');
    tr.innerHTML = `
      <td>${ev.employee_code}</td>
      <td>${ev.employee_name}</td>
      <td>${ev.department}</td>
      <td>${ev.evaluation_type}</td>
      <td>${formatDate(ev.start_date)}</td>
      <td>${formatDate(ev.end_date)}</td>
      <td>${ev.score} %</td>
      <td>${ev.notes || ''}</td>
      <td>
        ${hasPermission('edit_evaluation') ? `<button class="edit-btn" onclick="editEvaluation(${ev.id})" title="تعديل التقييم"><i class="fas fa-edit"></i> تعديل</button>` : ''}
        ${hasPermission('delete_evaluation') ? `<button class="delete-btn" onclick="deleteEvaluation(${ev.id})" title="حذف التقييم"><i class="fas fa-trash-alt"></i> حذف</button>` : ''}
      </td>
    `;
    tableBody.appendChild(tr);
  });
  
  // تطبيق الصلاحيات على الأزرار
  applyEvaluationPermissions();
}

// تطبيق الصلاحيات على أزرار التقييم
function applyEvaluationPermissions() {
  // انتظار قصير للتأكد من تحميل النظام الموحد
  setTimeout(() => {
    // البحث عن جميع الأزرار في جدول التقييم
    const tableBody = document.querySelector('#evaluation-table tbody');
    if (!tableBody) return;

    const buttons = tableBody.querySelectorAll('[data-permission]');

    buttons.forEach(button => {
      const permission = button.getAttribute('data-permission');

      if (permission) {
        // استخدام النظام الموحد للتحقق من الصلاحيات
        let hasPermissionValue = false;

        if (window.permissionManager && typeof window.permissionManager.hasPermission === 'function') {
          // استخدام النظام الموحد
          hasPermissionValue = window.permissionManager.hasPermission(permission);
        } else if (typeof hasPermission === 'function') {
          // استخدام الدالة المساعدة
          hasPermissionValue = hasPermission(permission);
        }

        // تطبيق الصلاحية
        if (!hasPermissionValue) {
          button.style.display = 'none';
        } else {
          button.style.display = 'inline-block';
        }
      }
    });

    // تطبيق النظام الموحد أيضاً إذا كان متاحاً
    if (window.permissionManager && typeof window.permissionManager.applyPermissionsToTable === 'function') {
      window.permissionManager.applyPermissionsToTable('evaluation-table');
    }
  }, 50);
}

function resetEvaluationForm() {
  document.getElementById('evaluationEmployeeSearch').value = '';
  document.getElementById('evaluationEmployeeCode').value = '';
  document.getElementById('evaluationEmployeeName').value = '';
  document.getElementById('evaluationEmployeeDepartment').value = '';
  document.getElementById('evaluationType').value = '';
  document.getElementById('evaluationStartDate').value = '';
  document.getElementById('evaluationEndDate').value = '';
  document.getElementById('evaluationScore').value = '';
  document.getElementById('evaluationNotes').value = '';
}

// فحص الصلاحيات
function hasPermission(permission) {
  try {
    const permissions = JSON.parse(localStorage.getItem('permissions') || '{}');
    const result = permissions[permission] === true;
    console.log(`[Evaluation] hasPermission(${permission}) = ${result}`, permissions);
    return result;
  } catch (error) {
    console.error('خطأ في قراءة الصلاحيات:', error);
    return false;
  }
}

async function deleteEvaluation(id) {
  // فحص صلاحية الحذف
  if (!hasPermission('delete_evaluation')) {
    alert('ليس لديك صلاحية لحذف التقييمات');
    return;
  }

  if (!confirm('هل أنت متأكد من حذف هذا التقييم؟')) return;
  try {
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/evaluations/${id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (response.ok) {
      alert('تم حذف التقييم بنجاح');
      displayEvaluationsWithDataTables();
    } else {
      const errorData = await response.json().catch(() => ({}));
      alert('فشل في حذف التقييم: ' + (errorData.error || 'خطأ غير معروف'));
    }
  } catch (error) {
    console.error('خطأ في حذف التقييم:', error);
    alert('حدث خطأ أثناء حذف التقييم. يرجى المحاولة مرة أخرى.');
  }
}

async function editEvaluation(id) {
  // فحص صلاحية التعديل
  if (!hasPermission('edit_evaluation')) {
    alert('ليس لديك صلاحية لتعديل التقييمات');
    return;
  }

  try {
    console.log('🔍 محاولة جلب التقييم رقم:', id);
    // جلب بيانات التقييم من الخادم
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/evaluations/${id}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    console.log('📡 استجابة الخادم:', response.status, response.statusText);

    if (!response.ok) {
      if (response.status === 404) {
        alert('لم يتم العثور على التقييم. قد يكون تم حذفه من قبل مستخدم آخر.');
      } else if (response.status === 403) {
        alert('ليس لديك صلاحية لعرض هذا التقييم');
      } else {
        alert('حدث خطأ في جلب بيانات التقييم');
      }
      return;
    }

    const evaluation = await response.json();

    // تعبئة النموذج بالبيانات
    document.getElementById('editEvaluationEmployeeCode').value = evaluation.employee_code;
    document.getElementById('editEvaluationEmployeeName').value = evaluation.employee_name || evaluation.employee_name;
    document.getElementById('editEvaluationEmployeeDepartment').value = evaluation.department;
    document.getElementById('editEvaluationType').value = evaluation.evaluation_type;

    // تنسيق التاريخ للحقول
    function formatDateForInput(dateString) {
      if (!dateString) return '';

      try {
        // إذا كان التاريخ بصيغة YYYY-MM-DD بالفعل، استخدمه مباشرة
        if (dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
          return dateString;
        }

        // تحويل التاريخ إلى صيغة YYYY-MM-DD
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return '';

        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');

        return `${year}-${month}-${day}`;
      } catch (error) {
        console.error('خطأ في تنسيق التاريخ:', error);
        return '';
      }
    }

    document.getElementById('editEvaluationStartDate').value = formatDateForInput(evaluation.start_date);
    document.getElementById('editEvaluationEndDate').value = formatDateForInput(evaluation.end_date);

    document.getElementById('editEvaluationScore').value = evaluation.score;
    document.getElementById('editEvaluationNotes').value = evaluation.notes || '';

    document.getElementById('updateEvaluationBtn').dataset.editId = id;

    document.getElementById('editEvaluationModal').style.display = 'block';

  } catch (error) {
    console.error('خطأ في جلب بيانات التقييم:', error);
    alert('حدث خطأ في جلب بيانات التقييم');
  }
}

function formatDate(dateString) {
  if (!dateString) return '';

  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '';

    // تنسيق التاريخ بصيغة DD/MM/YYYY
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();

    return `${day}/${month}/${year}`;
  } catch (error) {
    console.error('خطأ في تنسيق التاريخ:', error);
    return '';
  }
}

async function exportEvaluationsToExcel() {
  try {
    // جلب جميع التقييمات من الخادم
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/evaluations`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      alert('فشل في جلب بيانات التقييمات للتصدير');
      return;
    }

    const evaluationsData = await response.json();

    const ws_data = [
      ['كود الموظف','اسم الموظف','الإدارة','نوع التقييم','تاريخ البداية','تاريخ النهاية','درجة التقييم','ملاحظات']
    ];

    evaluationsData.forEach(ev => {
      ws_data.push([
        ev.employee_code,
        ev.employee_name,
        ev.department,
        ev.evaluation_type,
        formatDate(ev.start_date),
        formatDate(ev.end_date),
        ev.score,
        ev.notes || ''
      ]);
    });

    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet(ws_data);
    XLSX.utils.book_append_sheet(wb, ws, 'التقييمات');
    XLSX.writeFile(wb, 'التقييمات.xlsx');

    // عرض رسالة نجاح
    alert('تم تصدير التقييمات إلى ملف Excel بنجاح');

  } catch (error) {
    console.error('خطأ في تصدير التقييمات:', error);
    alert('حدث خطأ أثناء تصدير البيانات');
  }
}

// --- Reports Tab Logic ---
const reportStartDate = document.getElementById('reportStartDate');
const reportEndDate = document.getElementById('reportEndDate');
const reportDepartment = document.getElementById('reportDepartment');
const reportType = document.getElementById('reportType');
const totalEvaluationsValue = document.getElementById('totalEvaluationsValue');
const averageScoreValue = document.getElementById('averageScoreValue');
const topEmployeeValue = document.getElementById('topEmployeeValue');
const reportTableBody = document.querySelector('#evaluation-report-table tbody');

let allReportEvaluations = [];
let filteredReportEvaluations = [];

function loadReportDepartments() {
  const token = localStorage.getItem('token');
  fetch(`${API_URL}/departments`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  })
    .then(res => res.json())
    .then(departments => {
      const reportDepartment = document.getElementById('reportDepartment');
      if (reportDepartment) {
        reportDepartment.innerHTML = '<option value="">كل الإدارات</option>';
        departments.forEach(dep => {
          const opt = document.createElement('option');
          opt.value = dep;
          opt.textContent = dep;
          reportDepartment.appendChild(opt);
        });
      }
    })
    .catch(error => console.error('خطأ في تحميل الإدارات:', error));
}

function fetchAllEvaluationsForReport() {
  const token = localStorage.getItem('token');
  fetch(`${API_URL}/evaluations`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  })
    .then(res => res.json())
    .then(data => {
      allReportEvaluations = data;
      applyReportFiltersFunc();
    })
    .catch(error => {
      console.error('خطأ في تحميل التقييمات:', error);
      allReportEvaluations = [];
      applyReportFiltersFunc();
    });
}

function applyReportFiltersFunc() {
  let filtered = allReportEvaluations;
  
  const startDate = document.getElementById('reportStartDate')?.value;
  const endDate = document.getElementById('reportEndDate')?.value;
  const department = document.getElementById('reportDepartment')?.value;
  const type = document.getElementById('reportType')?.value;
  const minScore = document.getElementById('reportMinScore')?.value;
  const maxScore = document.getElementById('reportMaxScore')?.value;
  
  if (startDate) {
    const filterStartDate = new Date(startDate);
    filterStartDate.setHours(0, 0, 0, 0);
    filtered = filtered.filter(ev => {
      const evalStartDate = new Date(ev.start_date);
      evalStartDate.setHours(0, 0, 0, 0);
      return evalStartDate >= filterStartDate;
    });
  }
  if (endDate) {
    const filterEndDate = new Date(endDate);
    filterEndDate.setHours(23, 59, 59, 999);
    filtered = filtered.filter(ev => {
      const evalEndDate = new Date(ev.end_date);
      evalEndDate.setHours(0, 0, 0, 0);
      return evalEndDate <= filterEndDate;
    });
  }
  if (department) {
    filtered = filtered.filter(ev => ev.department === department);
  }
  if (type) {
    filtered = filtered.filter(ev => ev.evaluation_type === type);
  }
  if (minScore) {
    filtered = filtered.filter(ev => parseFloat(ev.score) >= parseFloat(minScore));
  }
  if (maxScore) {
    filtered = filtered.filter(ev => parseFloat(ev.score) <= parseFloat(maxScore));
  }
  
  filteredReportEvaluations = filtered;
  updateReportSummary();
  displayReportEvaluations();
}

function resetReportFiltersFunc() {
  const reportStartDate = document.getElementById('reportStartDate');
  const reportEndDate = document.getElementById('reportEndDate');
  const reportDepartment = document.getElementById('reportDepartment');
  const reportType = document.getElementById('reportType');
  const reportMinScore = document.getElementById('reportMinScore');
  const reportMaxScore = document.getElementById('reportMaxScore');
  
  if (reportStartDate) reportStartDate.value = '';
  if (reportEndDate) reportEndDate.value = '';
  if (reportDepartment) reportDepartment.value = '';
  if (reportType) reportType.value = '';
  if (reportMinScore) reportMinScore.value = '';
  if (reportMaxScore) reportMaxScore.value = '';
  
  applyReportFiltersFunc();
}

function updateReportSummary() {
  const totalEvaluations = filteredReportEvaluations.length;
  const averageScore = totalEvaluations > 0 
    ? (filteredReportEvaluations.reduce((sum, ev) => sum + parseFloat(ev.score), 0) / totalEvaluations).toFixed(1)
    : 0;
  
  const topEmployee = filteredReportEvaluations.length > 0
    ? filteredReportEvaluations.reduce((top, current) => 
        parseFloat(current.score) > parseFloat(top.score) ? current : top
      ).employee_name
    : 'لا يوجد';
  
  const totalEvaluationsValue = document.getElementById('totalEvaluationsValue');
  const averageScoreValue = document.getElementById('averageScoreValue');
  const topEmployeeValue = document.getElementById('topEmployeeValue');
  
  if (totalEvaluationsValue) totalEvaluationsValue.textContent = totalEvaluations;
  if (averageScoreValue) averageScoreValue.textContent = `${averageScore}%`;
  if (topEmployeeValue) topEmployeeValue.textContent = topEmployee;
}

function displayReportEvaluations() {
  const reportTableBody = document.querySelector('#evaluation-report-table tbody');
  if (!reportTableBody) return;
  
  reportTableBody.innerHTML = '';
  filteredReportEvaluations.forEach(ev => {
    const tr = document.createElement('tr');
    // دمج الإدارة مع الملاحظات إذا كانت الملاحظات موجودة
    let departmentCell = ev.department;
    let notesCell = ev.notes || '';
    if (notesCell && notesCell.trim() !== '') {
      notesCell = `<span class='notes-highlight'>${notesCell}</span>`;
    }
    tr.innerHTML = `
      <td>${ev.employee_code}</td>
      <td>${ev.employee_name}</td>
      <td class="department-column">${departmentCell}</td>
      <td>${ev.evaluation_type}</td>
      <td>${formatDate(ev.start_date)}</td>
      <td>${formatDate(ev.end_date)}</td>
      <td>${ev.score}%</td>
      <td class="notes-column">${notesCell}</td>
    `;
    reportTableBody.appendChild(tr);
  });
}

// البحث في التقييمات من السيرفر
async function searchEvaluationsFromServer() {
  try {
    const searchTerm = document.getElementById('searchEvaluation')?.value.trim() || '';

    const params = new URLSearchParams();
    if (searchTerm) {
      params.append('employee_name', searchTerm);
      params.append('employee_code', searchTerm);
    }

    let url = `${API_URL}/api/evaluations`;
    if (params.toString()) {
      url = `${API_URL}/api/evaluations/search?${params.toString()}`;
    }

    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });

    if (!response.ok) {
      throw new Error('فشل في جلب بيانات التقييمات');
    }

    const filteredEvaluations = await response.json();
    displayFilteredEvaluations(filteredEvaluations);

  } catch (error) {
    console.error('خطأ في البحث:', error);
    showNotification('خطأ في البحث عن التقييمات', 'error');
  }
}

// البحث في تقارير التقييمات من السيرفر
async function searchReportEvaluationsFromServer() {
  try {
    const searchTerm = document.getElementById('searchReportTable')?.value.trim() || '';

    const params = new URLSearchParams();
    if (searchTerm) {
      params.append('employee_name', searchTerm);
      params.append('employee_code', searchTerm);
    }

    let url = `${API_URL}/api/evaluations`;
    if (params.toString()) {
      url = `${API_URL}/api/evaluations/search?${params.toString()}`;
    }

    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });

    if (!response.ok) {
      throw new Error('فشل في جلب بيانات التقييمات');
    }

    const filteredEvaluations = await response.json();
    displayFilteredReportEvaluations(filteredEvaluations);

  } catch (error) {
    console.error('خطأ في البحث:', error);
    showNotification('خطأ في البحث عن التقييمات', 'error');
  }
}

function searchReportTableFunc() {
  searchReportEvaluationsFromServer();
}

function displayFilteredReportEvaluations(evaluationsToShow) {
  const reportTableBody = document.querySelector('#evaluation-report-table tbody');
  if (!reportTableBody) return;
  
  reportTableBody.innerHTML = '';
  evaluationsToShow.forEach(ev => {
    const tr = document.createElement('tr');
    tr.innerHTML = `
      <td>${ev.employee_code}</td>
      <td>${ev.employee_name}</td>
      <td>${ev.department}</td>
      <td>${ev.evaluation_type}</td>
      <td>${formatDate(ev.start_date)}</td>
      <td>${formatDate(ev.end_date)}</td>
      <td>${ev.score}%</td>
      <td>${ev.notes || ''}</td>
    `;
    reportTableBody.appendChild(tr);
  });
}

// وظائف تبويب أعلى وأقل التقييمات
function applyTopBottomFilters() {
  const startDate = document.getElementById('topBottomStartDate')?.value;
  const endDate = document.getElementById('topBottomEndDate')?.value;
  const department = document.getElementById('topBottomDepartment')?.value;
  const evaluationType = document.getElementById('topBottomType')?.value;
  const sortOrder = document.getElementById('sortOrder')?.value || 'desc';
  const limitResults = document.getElementById('limitResults')?.value;
  
  let filtered = [...evaluations];
  
  // تطبيق الفلاتر
  if (startDate) {
    const filterStartDate = new Date(startDate);
    filterStartDate.setHours(0, 0, 0, 0);
    filtered = filtered.filter(ev => {
      const evalStartDate = new Date(ev.start_date);
      evalStartDate.setHours(0, 0, 0, 0);
      return evalStartDate >= filterStartDate;
    });
  }
  if (endDate) {
    const filterEndDate = new Date(endDate);
    filterEndDate.setHours(23, 59, 59, 999);
    filtered = filtered.filter(ev => {
      const evalEndDate = new Date(ev.end_date);
      evalEndDate.setHours(0, 0, 0, 0);
      return evalEndDate <= filterEndDate;
    });
  }
  if (department) {
    filtered = filtered.filter(ev => ev.department === department);
  }
  if (evaluationType) {
    filtered = filtered.filter(ev => ev.evaluation_type === evaluationType);
  }
  
  // ترتيب النتائج
  filtered.sort((a, b) => {
    const scoreA = parseFloat(a.score);
    const scoreB = parseFloat(b.score);
    return sortOrder === 'desc' ? scoreB - scoreA : scoreA - scoreB;
  });
  
  // تحديد عدد النتائج
  if (limitResults && limitResults !== 'all') {
    filtered = filtered.slice(0, parseInt(limitResults));
  }
  
  displayTopBottomEvaluations(filtered);
}

function resetTopBottomFilters() {
  document.getElementById('topBottomStartDate').value = '';
  document.getElementById('topBottomEndDate').value = '';
  document.getElementById('topBottomDepartment').value = '';
  document.getElementById('topBottomType').value = '';
  document.getElementById('sortOrder').value = 'desc';
  document.getElementById('limitResults').value = '10';
  
  applyTopBottomFilters();
}

function displayTopBottomEvaluations(evaluationsToShow) {
  const tableBody = document.querySelector('#top-bottom-table tbody');
  if (!tableBody) return;
  
  tableBody.innerHTML = '';
  evaluationsToShow.forEach((ev, index) => {
    const tr = document.createElement('tr');
    tr.innerHTML = `
      <td>${index + 1}</td>
      <td>${ev.employee_code}</td>
      <td>${ev.employee_name}</td>
      <td>${ev.department}</td>
      <td>${ev.evaluation_type}</td>
      <td>${formatDate(ev.start_date)}</td>
      <td>${formatDate(ev.end_date)}</td>
      <td>${ev.score}%</td>
      <td>${ev.notes || ''}</td>
    `;
    tableBody.appendChild(tr);
  });
}

function searchInTopBottomTable(searchTerm) {
  const tableBody = document.querySelector('#top-bottom-table tbody');
  if (!tableBody) return;
  
  const rows = tableBody.querySelectorAll('tr');
  rows.forEach(row => {
    const text = row.textContent.toLowerCase();
    row.style.display = text.includes(searchTerm.toLowerCase()) ? '' : 'none';
  });
}

function exportTopBottomTable() {
  const table = document.getElementById('top-bottom-table');
  if (!table) return;
  
  exportTableToExcel(table, 'تقرير_أعلى_وأقل_التقييمات');
}

// وظائف تبويب الموظفين غير المقيمين
function applyUnevaluatedFilters() {
  const department = document.getElementById('unevaluatedDepartment')?.value;
  const evaluationType = document.getElementById('unevaluatedEvaluationType')?.value;
  const period = document.getElementById('unevaluatedPeriod')?.value;
  const customStartDate = document.getElementById('customStartDate')?.value;
  const customEndDate = document.getElementById('customEndDate')?.value;
  
  let startDate, endDate;
  const now = new Date();
  
  // تحديد الفترة
  switch (period) {
    case 'current_month':
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
      break;
    case 'last_month':
      startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      endDate = new Date(now.getFullYear(), now.getMonth(), 0);
      break;
    case 'current_quarter':
      const currentQuarter = Math.floor(now.getMonth() / 3);
      startDate = new Date(now.getFullYear(), currentQuarter * 3, 1);
      endDate = new Date(now.getFullYear(), (currentQuarter + 1) * 3, 0);
      break;
    case 'last_quarter':
      const lastQuarter = Math.floor(now.getMonth() / 3) - 1;
      if (lastQuarter < 0) {
        startDate = new Date(now.getFullYear() - 1, 9, 1);
        endDate = new Date(now.getFullYear() - 1, 11, 31);
      } else {
        startDate = new Date(now.getFullYear(), lastQuarter * 3, 1);
        endDate = new Date(now.getFullYear(), (lastQuarter + 1) * 3, 0);
      }
      break;
    case 'custom':
      if (customStartDate && customEndDate) {
        startDate = new Date(customStartDate);
        endDate = new Date(customEndDate);
      }
      break;
  }
  
  // العثور على الموظفين غير المقيمين
  let filteredEmployees = [...employees];
  
  if (department) {
    filteredEmployees = filteredEmployees.filter(emp => emp.department === department);
  }
  
  console.log('عدد الموظفين المفلترين:', filteredEmployees.length);
  console.log('عدد التقييمات المتاحة:', evaluations.length);
  console.log('الفترة المحددة:', { startDate, endDate });
  
  const unevaluatedEmployees = filteredEmployees.filter(employee => {
    // البحث عن التقييمات للموظف
    const employeeEvaluations = evaluations.filter(ev => {
      // التحقق من تطابق كود الموظف
      const codeMatch = ev.employee_code === employee.code || ev.employee_code === String(employee.code);
      if (!codeMatch) return false;
      
      // فلترة حسب نوع التقييم إذا تم تحديده
      if (evaluationType && ev.evaluation_type !== evaluationType) {
        return false;
      }
      
      // إذا لم يتم تحديد فترة، البحث عن أي تقييم
      if (!startDate && !endDate) {
        return true;
      }
      
      const evalStartDate = new Date(ev.start_date);
      const evalEndDate = new Date(ev.end_date);
      
      // التحقق من صحة التواريخ
      if (isNaN(evalStartDate.getTime()) || isNaN(evalEndDate.getTime())) {
        console.warn('تاريخ غير صحيح في التقييم:', ev);
        return false;
      }
      
      // التحقق من تداخل الفترات
      if (startDate && endDate) {
        return (evalStartDate <= endDate && evalEndDate >= startDate);
      }
      
      // إذا تم تحديد تاريخ البداية فقط
      if (startDate && !endDate) {
        return evalEndDate >= startDate;
      }
      
      // إذا تم تحديد تاريخ النهاية فقط
      if (!startDate && endDate) {
        return evalStartDate <= endDate;
      }
      
      return false;
    });
    
    console.log(`الموظف ${employee.code} - ${employee.full_name}: ${employeeEvaluations.length} تقييم`);
    return employeeEvaluations.length === 0;
  });
  
  console.log('عدد الموظفين غير المقيمين:', unevaluatedEmployees.length);
  
  displayUnevaluatedEmployees(unevaluatedEmployees, filteredEmployees.length);
}

function resetUnevaluatedFilters() {
  document.getElementById('unevaluatedDepartment').value = '';
  document.getElementById('unevaluatedEvaluationType').value = '';
  document.getElementById('unevaluatedPeriod').value = 'current_month';
  document.getElementById('customStartDate').value = '';
  document.getElementById('customEndDate').value = '';
  
  // إخفاء حقول الفترة المخصصة
  const customGroups = document.querySelectorAll('#customPeriodGroup, #customPeriodGroupEnd');
  customGroups.forEach(group => group.style.display = 'none');
  
  applyUnevaluatedFilters();
}

function displayUnevaluatedEmployees(unevaluatedEmployees, totalEmployees) {
  // تحديث البطاقات الإحصائية
  const unevaluatedCount = unevaluatedEmployees.length;
  const coverageValue = totalEmployees > 0 ? ((totalEmployees - unevaluatedCount) / totalEmployees * 100) : 0;
  const coverage = coverageValue % 1 === 0 ? parseInt(coverageValue) : coverageValue.toFixed(1);
  
  document.getElementById('unevaluatedCountValue').textContent = unevaluatedCount;
  document.getElementById('totalEmployeesValue').textContent = totalEmployees;
  document.getElementById('evaluationCoverageValue').textContent = `${coverage}%`;
  
  // عرض الجدول
  const tableBody = document.querySelector('#unevaluated-employees-table tbody');
  if (!tableBody) return;
  
  tableBody.innerHTML = '';
  unevaluatedEmployees.forEach(employee => {
    // العثور على آخر تقييم للموظف - استخدام code بدلاً من employee_code
    const lastEvaluation = evaluations
      .filter(ev => ev.employee_code === employee.code)
      .sort((a, b) => new Date(b.end_date) - new Date(a.end_date))[0];
    
    const lastEvaluationDate = lastEvaluation ? formatDate(lastEvaluation.end_date) : 'لم يتم تقييمه';
    const daysSinceLastEvaluation = lastEvaluation 
      ? Math.floor((new Date() - new Date(lastEvaluation.end_date)) / (1000 * 60 * 60 * 24))
      : 'غير محدد';
    
    const tr = document.createElement('tr');
    tr.innerHTML = `
      <td>${employee.code || 'غير محدد'}</td>
      <td>${employee.full_name || 'غير محدد'}</td>
      <td>${employee.department || 'غير محدد'}</td>
      <td>${employee.position || 'غير محدد'}</td>
      <td>${formatDate(employee.hire_date) || 'غير محدد'}</td>
      <td>${lastEvaluationDate}</td>
      <td>${daysSinceLastEvaluation !== 'غير محدد' ? daysSinceLastEvaluation + ' يوم' : daysSinceLastEvaluation}</td>
    `;
    tableBody.appendChild(tr);
  });
}

function searchInUnevaluatedTable(searchTerm) {
  const tableBody = document.querySelector('#unevaluated-employees-table tbody');
  if (!tableBody) return;
  
  const rows = tableBody.querySelectorAll('tr');
  rows.forEach(row => {
    const text = row.textContent.toLowerCase();
    row.style.display = text.includes(searchTerm.toLowerCase()) ? '' : 'none';
  });
}

function exportUnevaluatedTable() {
  const table = document.getElementById('unevaluated-employees-table');
  if (!table) return;
  
  exportTableToExcel(table, 'تقرير_الموظفين_غير_المقيمين');
}

// وظائف البحث في التقارير
function searchInReportTable(searchTerm) {
  searchReportTableFunc();
}

function exportReportTable() {
  const table = document.getElementById('evaluation-report-table');
  if (!table) return;
  
  exportTableToExcel(table, 'تقرير_التقييمات');
}

// تحديث وظيفة تحميل الإدارات لتشمل جميع القوائم المنسدلة
function populateDepartmentDropdowns() {
  const departmentSelects = [
    'evaluationEmployeeDepartment',
    'reportDepartment', 
    'topBottomDepartment',
    'unevaluatedDepartment'
  ];
  
  const departments = [...new Set(employees.map(emp => emp.department).filter(dept => dept && dept.trim() !== ''))];
  console.log('الإدارات المتاحة:', departments);
  
  departmentSelects.forEach(selectId => {
    const select = document.getElementById(selectId);
    if (select && selectId !== 'evaluationEmployeeDepartment') {
      // مسح الخيارات الحالية (عدا الخيار الأول)
      while (select.children.length > 1) {
        select.removeChild(select.lastChild);
      }
      
      // إضافة الإدارات
      departments.forEach(dept => {
        const option = document.createElement('option');
        option.value = dept;
        option.textContent = dept;
        select.appendChild(option);
      });
      
      console.log(`تم تحديث قائمة ${selectId} بـ ${departments.length} إدارة`);
    }
  });
}

// تحديث وظيفة تحميل الموظفين
async function loadEmployees() {
  try {
    // إضافة معامل لاستبعاد الموظفين المستقيلين
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/employees?include_resigned=false`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (response.ok) {
      employees = await response.json();
      populateDepartmentDropdowns();
      updateEmployeeSearchSuggestions();
    }
  } catch (error) {
    // خطأ في تحميل الموظفين
  }
}





function exportReportTableFunc() {
  const ws_data = [
    ['كود الموظف', 'اسم الموظف', 'الإدارة', 'نوع التقييم', 'تاريخ البداية', 'تاريخ النهاية', 'درجة التقييم', 'ملاحظات']
  ];
  
  filteredReportEvaluations.forEach(ev => {
    ws_data.push([
      ev.employee_code,
      ev.employee_name,
      ev.department,
      ev.evaluation_type,
      formatDate(ev.start_date),
      formatDate(ev.end_date),
      ev.score,
      ev.notes || ''
    ]);
  });
  
  const wb = XLSX.utils.book_new();
  const ws = XLSX.utils.aoa_to_sheet(ws_data);
  XLSX.utils.book_append_sheet(wb, ws, 'تقرير التقييمات');
  XLSX.writeFile(wb, 'تقرير_التقييمات.xlsx');
}

// إعداد فلاتر التقييمات
function setupEvaluationFilters() {
  const applyEvaluationFiltersBtn = document.getElementById('applyEvaluationFiltersBtn');
  const clearEvaluationFiltersBtn = document.getElementById('clearEvaluationFiltersBtn');

  if (applyEvaluationFiltersBtn) {
    applyEvaluationFiltersBtn.addEventListener('click', function() {
      applyEvaluationFilters();
    });
  }

  if (clearEvaluationFiltersBtn) {
    clearEvaluationFiltersBtn.addEventListener('click', function() {
      clearEvaluationFilters();
    });
  }

  // إزالة البحث التلقائي - سيتم البحث فقط عند الضغط على زر "تطبيق الفلاتر"
}

// تطبيق فلاتر التقييمات
function applyEvaluationFilters() {
  const employeeCode = document.getElementById('filterEvaluationEmployeeCode').value.trim();
  const employeeName = document.getElementById('filterEvaluationEmployeeName').value.trim();
  const fromDate = document.getElementById('filterEvaluationFromDate').value;
  const toDate = document.getElementById('filterEvaluationToDate').value;

  const tableBody = document.querySelector('#evaluation-table tbody');
  const rows = tableBody.querySelectorAll('tr');

  rows.forEach(row => {
    const cells = row.querySelectorAll('td');
    if (cells.length === 0) return; // تجاهل الصفوف الفارغة

    // استخراج البيانات من الخلايا
    const rowEmployeeCode = cells[0]?.textContent?.trim() || '';
    const rowEmployeeName = cells[1]?.textContent?.trim() || '';
    const rowStartDate = cells[4]?.textContent?.trim() || ''; // تاريخ البداية في العمود الخامس
    const rowEndDate = cells[5]?.textContent?.trim() || ''; // تاريخ النهاية في العمود السادس

    let showRow = true;

    // فلترة بالكود
    if (employeeCode && !rowEmployeeCode.includes(employeeCode)) {
      showRow = false;
    }

    // فلترة بالاسم
    if (employeeName && !rowEmployeeName.toLowerCase().includes(employeeName.toLowerCase())) {
      showRow = false;
    }

    // فلترة بالتاريخ من (مقارنة مع تاريخ البداية)
    if (fromDate && rowStartDate) {
      try {
        const rowDateObj = new Date(rowStartDate.split('/').reverse().join('-')); // تحويل من dd/mm/yyyy إلى yyyy-mm-dd
        const fromDateObj = new Date(fromDate);
        if (rowDateObj < fromDateObj) {
          showRow = false;
        }
      } catch (e) {
        if (rowStartDate < fromDate) {
          showRow = false;
        }
      }
    }

    // فلترة بالتاريخ إلى (مقارنة مع تاريخ النهاية)
    if (toDate && rowEndDate) {
      try {
        const rowDateObj = new Date(rowEndDate.split('/').reverse().join('-')); // تحويل من dd/mm/yyyy إلى yyyy-mm-dd
        const toDateObj = new Date(toDate);
        if (rowDateObj > toDateObj) {
          showRow = false;
        }
      } catch (e) {
        if (rowEndDate > toDate) {
          showRow = false;
        }
      }
    }

    // إظهار أو إخفاء الصف
    row.style.display = showRow ? '' : 'none';
  });
}

// مسح فلاتر التقييمات
function clearEvaluationFilters() {
  document.getElementById('filterEvaluationEmployeeCode').value = '';
  document.getElementById('filterEvaluationEmployeeName').value = '';
  document.getElementById('filterEvaluationFromDate').value = '';
  document.getElementById('filterEvaluationToDate').value = '';

  // إظهار جميع الصفوف
  const tableBody = document.querySelector('#evaluation-table tbody');
  const rows = tableBody.querySelectorAll('tr');
  rows.forEach(row => {
    row.style.display = '';
  });
}

// التحقق من المحتوى المحدد من URL
function checkSelectedContent() {
  // قراءة التبويب من URL
  const urlParams = new URLSearchParams(window.location.search);
  const tabFromUrl = urlParams.get('tab');

  if (tabFromUrl) {
    // تحويل اسم التبويب من URL إلى اسم المحتوى
    let contentType = '';
    switch(tabFromUrl) {
      case 'add-evaluation':
        contentType = 'add-evaluation';
        break;
      case 'view-evaluations':
        contentType = 'view-evaluations';
        break;
      case 'evaluation-reports':
        contentType = 'evaluation-reports';
        break;
      case 'top-bottom-evaluations':
        contentType = 'top-bottom-evaluations';
        break;
      case 'unevaluated-employees':
        contentType = 'unevaluated-employees';
        break;
      default:
        contentType = 'add-evaluation';
    }
    showContent(contentType);
  } else {
    // عرض المحتوى الافتراضي (إضافة تقييم)
    showContent('add-evaluation');
  }
}

// ===== وظائف DataTables للتقييمات =====

// التحقق من تحميل المكتبات المطلوبة
function checkLibrariesLoaded() {
  return typeof $ !== 'undefined' &&
         typeof $.fn.DataTable !== 'undefined' &&
         document.readyState === 'complete';
}

// تهيئة DataTables للتقييمات
function initializeEvaluationDataTables() {
  // التحقق من وجود التوكن
  const token = localStorage.getItem('token');
  if (!token) {
    console.error('لا يوجد توكن مصادقة - لا يمكن تهيئة DataTables');
    alert('يرجى تسجيل الدخول أولاً');
    window.location.href = 'login.html';
    return;
  }

  // التحقق من تحميل جميع المكتبات المطلوبة
  if (!checkLibrariesLoaded() || !document.getElementById('evaluationTable')) {
    console.log('انتظار تحميل المكتبات المطلوبة للتقييمات...');
    setTimeout(initializeEvaluationDataTables, 1000);
    return;
  }

  try {
    console.log('🚀 بدء تهيئة DataTables للتقييمات...');
    console.log('📡 API URL:', `${API_URL}/evaluations/datatables`);

    // تهيئة DataTables
    const table = $('#evaluationTable').DataTable({
      processing: true,
      serverSide: true,
      ajax: {
        url: `${API_URL}/evaluations/datatables`,
        type: 'GET',
        beforeSend: function(xhr) {
          const token = localStorage.getItem('token');
          if (token) {
            xhr.setRequestHeader('Authorization', `Bearer ${token}`);
          }
        },
        error: function(xhr, error, code) {
          console.error('❌ خطأ في تحميل بيانات التقييمات:', error, xhr);
          console.error('📊 تفاصيل الخطأ:', {
            status: xhr.status,
            statusText: xhr.statusText,
            responseText: xhr.responseText
          });

          if (xhr.status === 401 || xhr.status === 403) {
            if (typeof handleTokenExpired === 'function') {
              handleTokenExpired();
            } else {
              alert('انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.');
              window.location.href = 'login.html';
            }
          } else if (xhr.status === 404) {
            console.error('❌ API endpoint غير موجود: /api/evaluations/datatables');
            alert('خطأ: API endpoint للتقييمات غير موجود. يرجى التحقق من إعدادات الخادم.');
          } else {
            alert('حدث خطأ في تحميل بيانات التقييمات. يرجى المحاولة مرة أخرى.');
          }
        }
      },
      columns: [
        { data: 0, title: 'كود الموظف', width: '10%', className: 'text-center' },
        { data: 1, title: 'اسم الموظف', width: '20%' },
        { data: 2, title: 'الإدارة', width: '15%' },
        { data: 3, title: 'نوع التقييم', width: '10%', className: 'text-center' },
        { data: 4, title: 'تاريخ البداية', width: '10%', className: 'text-center' },
        { data: 5, title: 'تاريخ النهاية', width: '10%', className: 'text-center' },
        { data: 6, title: 'درجة التقييم', width: '10%', className: 'text-center' },
        { data: 7, title: 'ملاحظات', width: '15%' },
        { data: 8, title: 'الإجراءات', orderable: false, searchable: false, width: '10%', className: 'text-center' }
      ],
      order: [[4, 'desc']], // ترتيب حسب تاريخ البداية (الأحدث أولاً)
      pageLength: 15,
      lengthMenu: [[10, 15, 25, 50], [10, 15, 25, 50]],
      language: {
        search: 'البحث:',
        lengthMenu: 'عرض _MENU_ تقييم',
        info: 'عرض _START_ إلى _END_ من _TOTAL_ تقييم',
        infoEmpty: 'لا توجد تقييمات',
        infoFiltered: '(مفلتر من _MAX_ تقييم إجمالي)',
        paginate: {
          first: 'الأول',
          last: 'الأخير',
          next: 'التالي',
          previous: 'السابق'
        },
        processing: 'جاري تحميل التقييمات...',
        emptyTable: 'لم يتم إضافة أي تقييمات بعد',
        zeroRecords: 'لم يتم العثور على تقييمات مطابقة لبحثك',
        loadingRecords: 'جاري تحميل التقييمات...',
        infoThousands: ',',
        searchPlaceholder: 'ابحث في التقييمات...'
      },
      responsive: true,
      dom: 'Brtip',
      deferRender: true,
      stateSave: true,
      buttons: [
        {
          extend: 'excel',
          text: 'تصدير إلى Excel',
          className: 'btn btn-success',
          exportOptions: {
            columns: [0, 1, 2, 3, 4, 5, 6, 7] // استبعاد عمود الإجراءات
          }
        }
      ],
      drawCallback: function(settings) {
        // إعادة ربط أحداث الأزرار بعد كل رسم للجدول
        bindEvaluationActionButtons();

        // تطبيق الصلاحيات على الأزرار
        applyEvaluationPermissionsToButtons();
      },
      initComplete: function() {
        console.log('✅ تم تهيئة DataTables للتقييمات بنجاح');
        console.log('📊 الجدول جاهز لعرض البيانات');
      }
    });

    // حفظ مرجع الجدول للاستخدام في أماكن أخرى
    window.evaluationDataTable = table;

  } catch (error) {
    console.error('خطأ في تهيئة DataTables للتقييمات:', error);
    alert('حدث خطأ في تهيئة الجدول. يرجى إعادة تحميل الصفحة.');
  }
}

// ربط أحداث أزرار الإجراءات في DataTables التقييمات
function bindEvaluationActionButtons() {
  // ربط أزرار التعديل
  $('.edit-evaluation-btn').off('click').on('click', function() {
    const evaluationId = $(this).data('evaluation-id');

    // التحقق من الصلاحيات
    const permissionFunc = window.hasPermission || hasPermission;
    if (typeof permissionFunc === 'function' && !permissionFunc('edit_evaluation')) {
      alert('ليس لديك صلاحية لتعديل التقييمات');
      return;
    }

    // استدعاء دالة التعديل الموجودة
    editEvaluation(evaluationId);
  });

  // ربط أزرار الحذف
  $('.delete-evaluation-btn').off('click').on('click', function() {
    const evaluationId = $(this).data('evaluation-id');

    // التحقق من الصلاحيات
    const permissionFunc = window.hasPermission || hasPermission;
    if (typeof permissionFunc === 'function' && !permissionFunc('delete_evaluation')) {
      alert('ليس لديك صلاحية لحذف التقييمات');
      return;
    }

    if (confirm('هل أنت متأكد من حذف هذا التقييم؟')) {
      // استدعاء دالة الحذف الموجودة
      deleteEvaluation(evaluationId);
    }
  });
}

// تطبيق الصلاحيات على أزرار التقييمات
function applyEvaluationPermissionsToButtons() {
  // التحقق من وجود دالة hasPermission
  const permissionFunc = window.hasPermission || hasPermission;
  if (typeof permissionFunc !== 'function') {
    console.warn('دالة hasPermission غير متاحة');
    return;
  }

  // إخفاء أزرار التعديل إذا لم تكن هناك صلاحية
  if (!permissionFunc('edit_evaluation')) {
    $('.edit-evaluation-btn').hide();
  }

  // إخفاء أزرار الحذف إذا لم تكن هناك صلاحية
  if (!permissionFunc('delete_evaluation')) {
    $('.delete-evaluation-btn').hide();
  }
}

// إعداد فلاتر البحث المتقدمة للتقييمات
function setupAdvancedEvaluationSearch() {
  const applyBtn = document.getElementById('applyAdvancedEvaluationSearch');
  const clearBtn = document.getElementById('clearAdvancedEvaluationSearch');

  if (applyBtn) {
    applyBtn.addEventListener('click', applyAdvancedEvaluationFilters);
  }

  if (clearBtn) {
    clearBtn.addEventListener('click', clearAdvancedEvaluationFilters);
  }

  // البحث التلقائي عند تغيير الحقول
  const searchInputs = [
    'searchEvaluationEmployeeCode',
    'searchEvaluationEmployeeName',
    'searchEvaluationType',
    'searchEvaluationMinScore',
    'searchEvaluationMaxScore',
    'searchEvaluationDateFrom',
    'searchEvaluationDateTo'
  ];

  searchInputs.forEach(inputId => {
    const input = document.getElementById(inputId);
    if (input) {
      input.addEventListener('input', debounce(applyAdvancedEvaluationFilters, 500));
      input.addEventListener('change', applyAdvancedEvaluationFilters);
    }
  });
}

// دالة debounce لتأخير البحث
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// تطبيق الفلاتر المتقدمة للتقييمات
function applyAdvancedEvaluationFilters() {
  if (!window.evaluationDataTable) {
    return;
  }

  const filters = {
    employeeCode: document.getElementById('searchEvaluationEmployeeCode')?.value || '',
    employeeName: document.getElementById('searchEvaluationEmployeeName')?.value || '',
    evaluationType: document.getElementById('searchEvaluationType')?.value || '',
    minScore: document.getElementById('searchEvaluationMinScore')?.value || '',
    maxScore: document.getElementById('searchEvaluationMaxScore')?.value || '',
    dateFrom: document.getElementById('searchEvaluationDateFrom')?.value || '',
    dateTo: document.getElementById('searchEvaluationDateTo')?.value || ''
  };

  // تطبيق البحث على الأعمدة المحددة
  window.evaluationDataTable
    .column(0).search(filters.employeeCode)    // كود الموظف
    .column(1).search(filters.employeeName)    // اسم الموظف
    .column(3).search(filters.evaluationType)  // نوع التقييم
    .draw();

  // للدرجات والتواريخ، سنحتاج لمعالجة خاصة في الخادم
  if (filters.minScore || filters.maxScore || filters.dateFrom || filters.dateTo) {
    // إضافة معاملات إضافية للطلب
    const settings = window.evaluationDataTable.settings()[0];
    settings.ajax.data = function(d) {
      d.minScore = filters.minScore;
      d.maxScore = filters.maxScore;
      d.dateFrom = filters.dateFrom;
      d.dateTo = filters.dateTo;
      return d;
    };
    window.evaluationDataTable.draw();
  }
}

// مسح الفلاتر المتقدمة للتقييمات
function clearAdvancedEvaluationFilters() {
  // مسح جميع حقول البحث
  document.getElementById('searchEvaluationEmployeeCode').value = '';
  document.getElementById('searchEvaluationEmployeeName').value = '';
  document.getElementById('searchEvaluationType').value = '';
  document.getElementById('searchEvaluationMinScore').value = '';
  document.getElementById('searchEvaluationMaxScore').value = '';
  document.getElementById('searchEvaluationDateFrom').value = '';
  document.getElementById('searchEvaluationDateTo').value = '';

  // مسح الفلاتر من DataTable
  if (window.evaluationDataTable) {
    window.evaluationDataTable
      .columns().search('')
      .draw();

    // مسح المعاملات الإضافية
    const settings = window.evaluationDataTable.settings()[0];
    settings.ajax.data = function(d) {
      return d;
    };
    window.evaluationDataTable.draw();
  }
}

// تحديث عرض التقييمات (استبدال الدالة القديمة)
function displayEvaluationsWithDataTables() {
  // التحقق من وجود التوكن
  const token = localStorage.getItem('token');
  if (!token) {
    console.error('لا يوجد توكن مصادقة - لا يمكن تحديث البيانات');
    return;
  }

  // إعادة تحميل بيانات DataTables
  if (window.evaluationDataTable) {
    window.evaluationDataTable.ajax.reload(null, false);
  } else {
    // إذا لم يكن DataTables مُهيأ بعد، انتظر قليلاً ثم حاول مرة أخرى
    setTimeout(() => {
      if (window.evaluationDataTable) {
        window.evaluationDataTable.ajax.reload(null, false);
      } else {
        // إذا لم يكن مُهيأ بعد، حاول تهيئته
        initializeEvaluationDataTables();
      }
    }, 1000);
  }
}

// عرض المحتوى المحدد
function showContent(contentType) {
  console.log('عرض المحتوى:', contentType);

  // إخفاء جميع المحتويات
  const allContents = document.querySelectorAll('.tab-content');
  allContents.forEach(content => {
    content.classList.remove('active');
    content.style.display = 'none';
  });

  // عرض المحتوى المحدد
  const targetContent = document.getElementById(contentType);
  if (targetContent) {
    targetContent.classList.add('active');
    targetContent.style.display = 'block';

    // تحديث عنوان الصفحة
    const pageTitle = document.querySelector('h1');
    if (pageTitle) {
      if (contentType === 'add-evaluation') {
        pageTitle.textContent = 'إضافة تقييم';
      } else if (contentType === 'evaluation-reports') {
        pageTitle.textContent = 'تقارير التقييم';
      } else if (contentType === 'top-bottom-evaluations') {
        pageTitle.textContent = 'أعلى وأقل التقييمات';
      } else if (contentType === 'unevaluated-employees') {
        pageTitle.textContent = 'الموظفين غير المقيمين';
      }
    }

    // تحديث البيانات حسب نوع المحتوى
    if (contentType === 'add-evaluation') {
      // تحديث جدول التقييمات عند عرض تبويب إضافة تقييم
      displayEvaluationsWithDataTables();
    } else if (contentType === 'evaluation-reports') {
      loadEvaluations();
    } else if (contentType === 'top-bottom-evaluations') {
      loadTopBottomEvaluations();
    } else if (contentType === 'unevaluated-employees') {
      loadUnevaluatedEmployees();
    }
  } else {
    console.error('لم يتم العثور على المحتوى:', contentType);
  }
}

