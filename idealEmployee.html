<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Expires" content="0">
  <title>العامل المثالي</title>
  <link rel="stylesheet" href="style.css" />
  <link rel="stylesheet" href="shared-styles.css">
  <link rel="stylesheet" href="idealEmployee.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="no-sidebar-layout.css">

  <!-- DataTables CSS -->
  <link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">
  <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.dataTables.min.css">
  <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">

  <style>
    /* تخصيص DataTable للعربية */
    .dataTables_wrapper {
      direction: rtl;
      text-align: right;
    }

    .dataTables_filter {
      text-align: left;
      margin-bottom: 10px;
    }

    .dataTables_filter label {
      font-weight: normal;
      white-space: nowrap;
      text-align: left;
    }

    .dataTables_filter input {
      margin-left: 0.5em;
      display: inline-block;
      width: auto;
    }

    .dataTables_length {
      text-align: right;
    }

    .dataTables_info {
      text-align: right;
    }

    .dataTables_paginate {
      text-align: left;
    }

    .advanced-search-container {
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
    }

    .advanced-search-container h4 {
      margin-top: 0;
      margin-bottom: 15px;
      color: #495057;
    }

    .search-filters-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 15px;
      margin-bottom: 15px;
    }

    .filter-actions {
      display: flex;
      gap: 10px;
      justify-content: flex-start;
    }



    .search-btn, .reset-btn, .export-btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.3s;
    }

    .search-btn {
      background-color: #007bff;
      color: white;
    }

    .search-btn:hover {
      background-color: #0056b3;
    }

    .reset-btn {
      background-color: #6c757d;
      color: white;
    }

    .reset-btn:hover {
      background-color: #545b62;
    }

    .export-btn {
      background-color: #28a745;
      color: white;
    }

    .export-btn:hover {
      background-color: #1e7e34;
    }

    /* تحسين أزرار الإجراءات في الجدول */
    .edit-btn, .delete-btn {
      padding: 4px 8px;
      margin: 2px;
      border: none;
      border-radius: 3px;
      cursor: pointer;
      font-size: 12px;
    }

    .edit-btn {
      background-color: #ffc107;
      color: #212529;
    }

    .edit-btn:hover {
      background-color: #e0a800;
    }

    .delete-btn {
      background-color: #dc3545;
      color: white;
    }

    .delete-btn:hover {
      background-color: #c82333;
    }

    /* تحسين تنسيق الجدول */
    #idealEmployeesDataTable {
      width: 100% !important;
      border-collapse: collapse !important;
      table-layout: fixed;
      margin: 0 !important;
      border-spacing: 0 !important;
    }

    /* إزالة جميع الحدود والفراغات الإضافية */
    .dataTables_wrapper {
      border: none !important;
      margin: 0 !important;
      padding: 0 !important;
    }

    #idealEmployeesDataTable th,
    #idealEmployeesDataTable td {
      text-align: center;
      vertical-align: middle;
      padding: 8px;
      border: 1px solid #dee2e6;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    #idealEmployeesDataTable th {
      background-color: #f8f9fa !important;
      font-weight: bold;
      color: #495057;
      position: relative;
    }

    /* إصلاح مشكلة تقسيم رأس الجدول */
    #idealEmployeesDataTable thead th {
      border-bottom: 1px solid #dee2e6 !important;
      background-color: #f8f9fa !important;
      border-top: none !important;
    }

    /* إزالة الفراغات والحدود الإضافية */
    #idealEmployeesDataTable thead {
      background-color: #f8f9fa !important;
    }

    #idealEmployeesDataTable tbody tr:first-child td {
      border-top: none !important;
    }

    /* إصلاح مشكلة الفراغ الأبيض */
    .dataTables_wrapper .dataTables_scroll {
      border: none !important;
    }

    .dataTables_wrapper .dataTables_scrollHead {
      border: none !important;
      background: transparent !important;
    }

    .dataTables_wrapper .dataTables_scrollBody {
      border: none !important;
      margin-top: 0 !important;
    }

    .dataTables_wrapper .dataTables_scrollHead table {
      margin-bottom: 0 !important;
    }

    .dataTables_wrapper .dataTables_scrollBody table {
      margin-top: 0 !important;
    }

    /* إزالة أي حدود أو فراغات إضافية من DataTable */
    table.dataTable {
      border-collapse: collapse !important;
      border-spacing: 0 !important;
      width: 100% !important;
      margin: 0 !important;
    }

    table.dataTable thead th,
    table.dataTable thead td {
      border-bottom: 1px solid #ddd !important;
      border-top: none !important;
      padding: 8px !important;
    }

    table.dataTable tbody th,
    table.dataTable tbody td {
      border-top: 1px solid #ddd !important;
      padding: 8px !important;
    }

    table.dataTable tbody tr:first-child th,
    table.dataTable tbody tr:first-child td {
      border-top: none !important;
    }

    /* إزالة أي خطوط أو حدود إضافية */
    .dataTables_wrapper .dataTables_scroll div.dataTables_scrollBody {
      border: none !important;
      background: white !important;
    }

    .dataTables_wrapper .dataTables_scroll div.dataTables_scrollHead {
      border: none !important;
      background: white !important;
    }

    .dataTables_wrapper .dataTables_scroll div.dataTables_scrollHead table {
      border-bottom: none !important;
    }

    /* إزالة أي خطوط خضراء أو ملونة */
    #idealEmployeesDataTable,
    #idealEmployeesDataTable *,
    .dataTables_wrapper,
    .dataTables_wrapper * {
      border-color: #dee2e6 !important;
      outline: none !important;
      background-color: transparent !important;
    }

    /* إزالة أي ألوان خضراء من رأس الجدول */
    #idealEmployeesDataTable thead,
    #idealEmployeesDataTable thead th,
    #idealEmployeesDataTable thead tr {
      background-color: #f8f9fa !important;
      border-color: #dee2e6 !important;
    }

    /* إزالة أي حدود ملونة */
    .dataTables_wrapper .dataTables_scroll,
    .dataTables_wrapper .dataTables_scrollHead,
    .dataTables_wrapper .dataTables_scrollBody {
      border: none !important;
      background: white !important;
    }

    /* إزالة أي خلفيات ملونة غير مرغوب فيها */
    .dataTables_wrapper .dataTables_scroll {
      background: white !important;
    }

    .dataTables_wrapper .dataTables_scrollHead,
    .dataTables_wrapper .dataTables_scrollBody {
      background: white !important;
    }

    /* إزالة أي حدود ملونة من الجدول */
    table.dataTable.no-footer {
      border-bottom: 1px solid #dee2e6 !important;
    }

    /* إزالة أي تأثيرات hover أو focus ملونة */
    #idealEmployeesDataTable tbody tr:hover {
      background-color: #f5f5f5 !important;
    }

    /* إزالة أي خطوط أو حدود من DataTable */
    .dataTables_wrapper .dataTables_scroll div {
      border: none !important;
      outline: none !important;
      box-shadow: none !important;
    }

    /* إزالة أي ألوان أو مستطيلات من DataTable */
    table.dataTable.no-footer {
      border-bottom: none !important;
    }

    .dataTables_wrapper .dataTables_processing {
      background: white !important;
      border: 1px solid #dee2e6 !important;
      color: #495057 !important;
    }

    /* إزالة أي تأثيرات ملونة من الأزرار */
    .dataTables_wrapper .dataTables_paginate .paginate_button {
      background: white !important;
      border: 1px solid #dee2e6 !important;
      color: #495057 !important;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
      background: #f8f9fa !important;
      border: 1px solid #dee2e6 !important;
      color: #495057 !important;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
      background: #007bff !important;
      border: 1px solid #007bff !important;
      color: white !important;
    }

    /* إزالة أي خلفيات ملونة من الجدول */
    #idealEmployeesDataTable tbody tr {
      background: white !important;
    }

    #idealEmployeesDataTable tbody tr:nth-child(even) {
      background: #f8f9fa !important;
    }

    #idealEmployeesDataTable tbody tr:hover {
      background: #e9ecef !important;
    }

    /* إخفاء حقل البحث العام من DataTable */
    .dataTables_wrapper .dataTables_filter {
      display: none !important;
    }

    /* إخفاء أي عناصر بحث إضافية */
    .dataTables_wrapper .dataTables_filter input {
      display: none !important;
    }

    /* تحسين تنسيق الأزرار */
    .dataTables_wrapper .dt-buttons {
      margin-bottom: 10px;
    }

    /* تحسين تنسيق معلومات الجدول */
    .dataTables_wrapper .dataTables_info {
      padding-top: 8px;
    }

    /* تحسين تنسيق عدد الصفوف */
    .dataTables_wrapper .dataTables_length {
      margin-bottom: 10px;
    }

    /* تحسين تنسيق الجدول ليكون مثل جدول الإجازات */
    .text-center {
      text-align: center !important;
    }

    #idealEmployeesDataTable.display {
      width: 100%;
    }

    #idealEmployeesDataTable.display thead th {
      background-color: #f8f9fa;
      border-bottom: 1px solid #dee2e6;
      font-weight: bold;
      padding: 12px 8px;
    }

    #idealEmployeesDataTable.display tbody td {
      padding: 8px;
      border-top: 1px solid #dee2e6;
    }

    #idealEmployeesDataTable.display tbody tr:nth-child(even) {
      background-color: #f8f9fa;
    }

    #idealEmployeesDataTable.display tbody tr:hover {
      background-color: #e9ecef;
    }

    /* تحسين أزرار الإجراءات */
    .edit-btn, .delete-btn {
      padding: 4px 8px;
      margin: 2px;
      border: none;
      border-radius: 3px;
      cursor: pointer;
      font-size: 12px;
      text-decoration: none;
      display: inline-block;
    }

    .edit-btn {
      background-color: #007bff;
      color: white;
    }

    .edit-btn:hover {
      background-color: #0056b3;
    }

    .delete-btn {
      background-color: #dc3545;
      color: white;
    }

    .delete-btn:hover {
      background-color: #c82333;
    }

    /* تحديد عرض الأعمدة */
    #idealEmployeesDataTable th:nth-child(1) { width: 10%; } /* كود الموظف */
    #idealEmployeesDataTable th:nth-child(2) { width: 15%; } /* الاسم */
    #idealEmployeesDataTable th:nth-child(3) { width: 12%; } /* الإدارة */
    #idealEmployeesDataTable th:nth-child(4) { width: 10%; } /* من فترة */
    #idealEmployeesDataTable th:nth-child(5) { width: 10%; } /* إلى فترة */
    #idealEmployeesDataTable th:nth-child(6) { width: 10%; } /* درجة التقييم */
    #idealEmployeesDataTable th:nth-child(7) { width: 10%; } /* مبلغ المكافأة */
    #idealEmployeesDataTable th:nth-child(8) { width: 15%; } /* سبب الاختيار */
    #idealEmployeesDataTable th:nth-child(9) { width: 8%; }  /* الإجراءات */

    #idealEmployeesDataTable tbody tr:nth-child(even) {
      background-color: #f8f9fa;
    }

    #idealEmployeesDataTable tbody tr:hover {
      background-color: #e9ecef;
    }

    /* تحسين عرض DataTable */
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_processing,
    .dataTables_wrapper .dataTables_paginate {
      color: #495057;
      margin: 10px 0;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button {
      padding: 0.375rem 0.75rem;
      margin-left: 2px;
      line-height: 1.25;
      color: #007bff;
      text-decoration: none;
      background-color: #fff;
      border: 1px solid #dee2e6;
      border-radius: 0.25rem;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
      color: #0056b3;
      background-color: #e9ecef;
      border-color: #dee2e6;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
      color: #fff;
      background-color: #007bff;
      border-color: #007bff;
    }

    /* تحسين البحث */
    .dataTables_wrapper .dataTables_filter input {
      border: 1px solid #ced4da;
      border-radius: 0.25rem;
      padding: 0.375rem 0.75rem;
      font-size: 0.875rem;
      line-height: 1.5;
    }

    /* تحسين عرض المعلومات */
    .dataTables_wrapper .dataTables_info {
      font-size: 0.875rem;
    }

    /* تحسين عرض التحميل */
    .dataTables_wrapper .dataTables_processing {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 200px;
      margin-left: -100px;
      margin-top: -26px;
      text-align: center;
      padding: 1rem;
      background: rgba(255, 255, 255, 0.9);
      border: 1px solid #ddd;
      border-radius: 4px;
    }
  </style>

  <script src="permissions.js" defer></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<body class="ideal-employee-page">

    <!-- زر العودة للوحة التحكم -->
  <div class="back-to-dashboard">
    <a href="ideal-employee-cards.html" class="dashboard-btn">
      <i class="fas fa-arrow-right"></i>
      <span>العودة لإدارة العامل المثالي</span>
    </a>
  </div>


  <div class="main-content full-width" id="mainContent">
    <h1>العامل المثالي</h1>



    <!-- محتوى التبويبات -->
    <div class="tab-content" id="add-ideal-employee">
      <div class="ideal-employee-form">
        <h2>إضافة عامل مثالي جديد</h2>
        
        <form id="idealEmployeeForm">
          <div class="form-row">
            <div class="form-group">
              <label for="employeeSearchAdd">البحث عن الموظف (بالاسم أو الكود):</label>
              <input type="text" id="employeeSearchAdd" class="employee-search-input" placeholder="أدخل اسم أو كود الموظف" list="employeeSearchSuggestions" autocomplete="off">
              <datalist id="employeeSearchSuggestions"></datalist>
            </div>
            <div class="form-group">
              <label for="employeeCode">كود الموظف:</label>
              <input type="text" id="employeeCode" placeholder="كود الموظف" readonly>
            </div>
            <div class="form-group">
              <label for="employeeName">الاسم:</label>
              <input type="text" id="employeeName" placeholder="اسم الموظف" readonly>
            </div>
            <div class="form-group">
              <label for="employeeDepartment">الإدارة:</label>
              <input type="text" id="employeeDepartment" placeholder="الإدارة" readonly>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="fromPeriod">من فترة:</label>
              <input type="date" id="fromPeriod" required>
            </div>
            <div class="form-group">
              <label for="toPeriod">إلى فترة:</label>
              <input type="date" id="toPeriod" required>
            </div>
            <div class="form-group">
              <label for="evaluationScore">درجة التقييم:</label>
              <input type="number" id="evaluationScore" min="0" max="100" step="0.1" placeholder="درجة التقييم" required>
            </div>
            <div class="form-group">
              <label for="rewardAmount">مبلغ المكافأة:</label>
              <input type="number" id="rewardAmount" min="0" step="0.01" placeholder="مبلغ المكافأة" required>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="selectionReason">سبب اختيار الموظف:</label>
              <input type="text" id="selectionReason" placeholder="سبب اختيار الموظف" required>
            </div>
            <div class="form-group">
              <label for="notes">ملاحظات:</label>
              <textarea id="notes" placeholder="ملاحظات إضافية" rows="3"></textarea>
            </div>
            <div class="form-group"></div>
            <div class="form-group"></div>
          </div>

          <div class="form-actions">
            <button type="submit" id="saveIdealEmployee" class="save-btn">
              <i class="fas fa-save"></i>
              حفظ العامل المثالي
            </button>
            <button type="button" id="resetForm" class="reset-btn">
              <i class="fas fa-undo"></i>
              إعادة تعيين
            </button>
          </div>
        </form>
      </div>

      <!-- جدول العمال المثاليين -->
      <div class="ideal-employees-table-container">
        <h3>قائمة العمال المثاليين</h3>

        <!-- فلاتر البحث المتقدم -->
        <div class="advanced-search-container">
          <h4>البحث المتقدم</h4>
          <div class="search-filters-row">
            <div class="form-group">
              <label for="searchIdealEmployeeCode">كود الموظف:</label>
              <input type="text" id="searchIdealEmployeeCode" placeholder="أدخل كود الموظف" class="filter-input">
            </div>

            <div class="form-group">
              <label for="searchIdealEmployeeName">اسم الموظف:</label>
              <input type="text" id="searchIdealEmployeeName" placeholder="أدخل اسم الموظف" class="filter-input">
            </div>

            <div class="form-group">
              <label for="searchIdealDepartment">الإدارة:</label>
              <input type="text" id="searchIdealDepartment" placeholder="أدخل الإدارة" class="filter-input">
            </div>

            <div class="form-group">
              <label for="searchIdealSelectionReason">سبب الاختيار:</label>
              <input type="text" id="searchIdealSelectionReason" placeholder="أدخل سبب الاختيار" class="filter-input">
            </div>
          </div>

          <div class="search-filters-row">
            <div class="form-group">
              <label for="searchIdealMinScore">أقل درجة تقييم:</label>
              <input type="number" id="searchIdealMinScore" placeholder="أقل درجة" class="filter-input" min="0" max="100" step="0.1">
            </div>

            <div class="form-group">
              <label for="searchIdealMaxScore">أعلى درجة تقييم:</label>
              <input type="number" id="searchIdealMaxScore" placeholder="أعلى درجة" class="filter-input" min="0" max="100" step="0.1">
            </div>

            <div class="form-group">
              <label for="searchIdealMinReward">أقل مبلغ مكافأة:</label>
              <input type="number" id="searchIdealMinReward" placeholder="أقل مبلغ" class="filter-input" min="0" step="0.01">
            </div>

            <div class="form-group">
              <label for="searchIdealMaxReward">أعلى مبلغ مكافأة:</label>
              <input type="number" id="searchIdealMaxReward" placeholder="أعلى مبلغ" class="filter-input" min="0" step="0.01">
            </div>
          </div>

          <div class="search-filters-row">
            <div class="form-group">
              <label for="searchIdealDateFrom">من تاريخ:</label>
              <input type="date" id="searchIdealDateFrom" class="filter-input">
            </div>

            <div class="form-group">
              <label for="searchIdealDateTo">إلى تاريخ:</label>
              <input type="date" id="searchIdealDateTo" class="filter-input">
            </div>
          </div>

          <div class="filter-actions">
            <button id="applyIdealAdvancedSearch" class="search-btn">تطبيق البحث</button>
            <button id="clearIdealAdvancedSearch" class="reset-btn">مسح البحث</button>
          </div>
        </div>



        <table id="idealEmployeesDataTable" class="ideal-employees-table display nowrap" style="width:100%">
          <thead>
            <tr>
              <th>كود الموظف</th>
              <th>الاسم</th>
              <th>الإدارة</th>
              <th>من فترة</th>
              <th>إلى فترة</th>
              <th>درجة التقييم</th>
              <th>مبلغ المكافأة</th>
              <th>سبب الاختيار</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody></tbody>
        </table>
      </div>

    <!-- تبويب التقارير -->
    <div class="tab-content" id="reports">
      <div class="reports-container">
        <h2>تقارير العامل المثالي</h2>

        <!-- فلاتر التقارير -->
        <div class="report-filters">
          <div class="filter-row">
            <div class="filter-group">
              <label for="reportStartDate">من تاريخ:</label>
              <input type="date" id="reportStartDate">
            </div>
            <div class="filter-group">
              <label for="reportEndDate">إلى تاريخ:</label>
              <input type="date" id="reportEndDate">
            </div>
            <div class="filter-group">
              <label for="reportDepartment">الإدارة:</label>
              <select id="reportDepartment">
                <option value="">جميع الإدارات</option>
              </select>
            </div>
            <div class="filter-group">
              <label for="reportEmployee">الموظف:</label>
              <input type="text" id="reportEmployee" placeholder="البحث عن موظف" list="reportEmployeeSuggestions" autocomplete="off">
              <datalist id="reportEmployeeSuggestions"></datalist>
            </div>
          </div>
          <div class="filter-actions">
            <button id="generateReport" class="generate-btn">
              <i class="fas fa-search"></i>
              إنشاء التقرير
            </button>
            <button id="resetFilters" class="reset-btn">
              <i class="fas fa-undo"></i>
              إعادة تعيين
            </button>
            <button id="printReport" class="print-btn">
              <i class="fas fa-print"></i>
              طباعة
            </button>
            <button id="exportReport" class="export-btn">
              <i class="fas fa-file-excel"></i>
              تصدير Excel
            </button>
          </div>
        </div>

        <!-- جدول التقارير -->
        <div class="report-table-container">
          <table class="report-table">
            <thead>
              <tr>
                <th>كود الموظف</th>
                <th>الاسم</th>
                <th>الإدارة</th>
                <th>من فترة</th>
                <th>إلى فترة</th>
                <th>درجة التقييم</th>
                <th>مبلغ المكافأة</th>
                <th>سبب الاختيار</th>
                <th>ملاحظات</th>
                <th>تاريخ الإضافة</th>
              </tr>
            </thead>
            <tbody id="reportTableBody">
              <!-- سيتم ملء البيانات ديناميكياً -->
            </tbody>
          </table>
        </div>

        <!-- إحصائيات التقرير -->
        <div class="report-statistics">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-users"></i>
            </div>
            <div class="stat-info">
              <h4>إجمالي العمال المثاليين</h4>
              <span id="totalIdealEmployees">0</span>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-money-bill-wave"></i>
            </div>
            <div class="stat-info">
              <h4>إجمالي المكافآت</h4>
              <span id="totalRewards">0</span>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-star"></i>
            </div>
            <div class="stat-info">
              <h4>متوسط التقييم</h4>
              <span id="averageScore">0</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- نافذة تعديل العامل المثالي -->
  <div class="modal-overlay" id="editIdealEmployeeModal" style="display: none;" onclick="closeEditModal()">
    <div class="modal-content" onclick="event.stopPropagation()">
      <div class="modal-header">
        <h3>تعديل العامل المثالي</h3>
        <button class="modal-close" onclick="closeEditModal()">&times;</button>
      </div>

      <div class="modal-body">
        <form id="editIdealEmployeeForm">
          <input type="hidden" id="editId">

          <div class="form-row">
            <div class="form-group">
              <label for="editEmployeeCode">كود الموظف:</label>
              <input type="text" id="editEmployeeCode" readonly>
            </div>
            <div class="form-group">
              <label for="editEmployeeName">الاسم:</label>
              <input type="text" id="editEmployeeName" readonly>
            </div>
            <div class="form-group">
              <label for="editDepartment">القسم:</label>
              <input type="text" id="editDepartment" readonly>
            </div>
            <div class="form-group">
              <label for="editFromPeriod">من فترة:</label>
              <input type="date" id="editFromPeriod" required>
            </div>
            <div class="form-group">
              <label for="editToPeriod">إلى فترة:</label>
              <input type="date" id="editToPeriod" required>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="editEvaluationScore">درجة التقييم:</label>
              <input type="number" id="editEvaluationScore" min="0" max="100" step="1" required>
            </div>
            <div class="form-group">
              <label for="editRewardAmount">مبلغ المكافأة:</label>
              <input type="number" id="editRewardAmount" min="0" step="1" required>
            </div>
            <div class="form-group">
              <label for="editSelectionReason">سبب اختيار الموظف:</label>
              <input type="text" id="editSelectionReason" required>
            </div>
            <div class="form-group">
              <label for="editNotes">ملاحظات:</label>
              <textarea id="editNotes" rows="3"></textarea>
            </div>
          </div>
        </form>
      </div>

      <div class="modal-footer">
        <button id="updateIdealEmployee" class="save-btn">تحديث العامل المثالي</button>
        <button type="button" class="cancel-btn" onclick="closeEditModal()">إلغاء</button>
      </div>
    </div>
  </div>

  <!-- jQuery (مطلوب لـ DataTables) -->
  <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

  <!-- DataTables JavaScript -->
  <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
  <script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
  <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
  <script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>

  <script src="shared-utils.js"></script>
  <script src="dateUtils.js"></script>
  <script src="arabic-date-picker.js"></script>
  <script src="permissions.js"></script>
  <script src="idealEmployee.js"></script>

</body>
</html>
