const express = require('express');
const router = express.Router();
const { logAction } = require('../activityLogger');
const { authenticateToken } = require('../middleware/auth');
const { addDepartmentFilter } = require('../middleware/departmentFilter');
const { cleanUpdateData, cleanInsertData, prepareUpdateQuery, prepareInsertQuery } = require('../utils/dataCleanup');

console.log('📚 تم تحميل routes/training.js');

/**
 * دالة لإضافة فلترة الإدارات لاستعلامات التدريب
 * @param {string} whereClause - شرط WHERE الحالي
 * @param {Array} queryParams - معاملات الاستعلام الحالية
 * @param {Object} req - كائن الطلب
 * @returns {Object} - كائن يحتوي على whereClause و queryParams المحدثة
 */
function addDepartmentFilterToTraining(whereClause, queryParams, req) {
  console.log(`🔍 Training Filter - allowedDepartments:`, req.allowedDepartments);

  if (req.allowedDepartments === null) {
    // المستخدم admin - لا نطبق فلترة
    console.log(`👑 Admin user - no filtering applied to training`);
    return { whereClause, queryParams };
  } else if (req.allowedDepartments && req.allowedDepartments.length > 0) {
    // تطبيق فلترة الإدارات المسموحة - فلترة على الإدارة الحالية للموظف
    const departmentPlaceholders = req.allowedDepartments.map(() => '?').join(',');
    whereClause += ` AND e.department IN (${departmentPlaceholders}) AND e.department IS NOT NULL`;
    queryParams.push(...req.allowedDepartments);
    console.log(`✅ Training filter applied for departments:`, req.allowedDepartments);
    console.log(`📝 Final WHERE clause:`, whereClause);
    return { whereClause, queryParams };
  } else {
    // لا يوجد إدارات مسموحة - لا يعرض أي نتائج
    whereClause += ` AND 1 = 0`;
    console.log(`🚫 No departments allowed - blocking all training data`);
    console.log(`📝 Final WHERE clause:`, whereClause);
    return { whereClause, queryParams };
  }
}

// متغير لتتبع ما إذا تم إنشاء الجدول أم لا
let trainingTableCreated = false;

// دالة لإنشاء جدول التدريب
const createTrainingTable = async (pool) => {
  if (trainingTableCreated) return; // إذا تم إنشاء الجدول بالفعل، لا تفعل شيئاً

  const sql = `CREATE TABLE IF NOT EXISTS training_courses (
    id int NOT NULL AUTO_INCREMENT,
    employee_code varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'كود الموظف',
    employee_name varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'اسم الموظف',
    department varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'الإدارة',
    course_name varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'اسم الدورة',
    course_date date NOT NULL COMMENT 'تاريخ الدورة',
    course_duration int NOT NULL COMMENT 'مدة الدورة بالأيام',
    training_type enum('تدريب داخلي','تدريب خارجي','تدريب خاص') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'نوع التدريب',
    course_cost decimal(10,2) DEFAULT '0.00' COMMENT 'تكلفة الدورة',
    notes text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'ملاحظات',
    created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ التحديث',
    PRIMARY KEY (id),
    KEY idx_employee_code (employee_code),
    KEY idx_department (department),
    KEY idx_course_date (course_date),
    KEY idx_training_type (training_type),
    KEY idx_course_name (course_name),
    KEY idx_training_courses_employee_code (employee_code),
    KEY idx_training_courses_course_date (course_date),
    KEY idx_training_courses_training_type (training_type)
  ) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الدورات التدريبية'`;

  await pool.promise().query(sql);
  trainingTableCreated = true; // تعيين المتغير إلى true بعد إنشاء الجدول
};

// إنشاء جدول التدريب
router.get('/create-table', async (req, res) => {
  try {
    await createTrainingTable(req.app.locals.pool);
    res.json({ message: 'تم التحقق من جدول التدريب' });
  } catch (error) {
    console.error('خطأ في إنشاء جدول التدريب:', error);
    res.status(500).json({ error: 'فشل في إنشاء جدول التدريب' });
  }
});

// البحث في الدورات التدريبية
router.get('/search', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    await createTrainingTable(pool);

    const {
      employee_code,
      employee_name,
      course_name,
      training_type,
      department,
      start_date,
      end_date
    } = req.query;

    let query = `
      SELECT * FROM training_courses
      WHERE 1=1
    `;
    const params = [];

    // البحث بكود الموظف
    if (employee_code) {
      query += " AND employee_code = ?";
      params.push(employee_code);
    }

    // البحث باسم الموظف
    if (employee_name) {
      query += " AND employee_name LIKE ?";
      params.push(`%${employee_name}%`);
    }

    // البحث باسم الدورة
    if (course_name) {
      query += " AND course_name LIKE ?";
      params.push(`%${course_name}%`);
    }

    // البحث بنوع التدريب
    if (training_type) {
      query += " AND training_type = ?";
      params.push(training_type);
    }

    // البحث بالإدارة
    if (department) {
      query += " AND department = ?";
      params.push(department);
    }

    // البحث بنطاق التاريخ
    if (start_date) {
      query += " AND start_date >= ?";
      params.push(start_date);
    }

    if (end_date) {
      query += " AND end_date <= ?";
      params.push(end_date);
    }

    query += " ORDER BY created_at DESC, id DESC";

    const [rows] = await pool.promise().query(query, params);
    res.json(rows);
  } catch (error) {
    console.error('خطأ في البحث في الدورات التدريبية:', error);
    res.status(500).json({ error: 'فشل في البحث في الدورات التدريبية' });
  }
});

// الحصول على جميع الدورات التدريبية
router.get('/', authenticateToken, addDepartmentFilter, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    await createTrainingTable(pool);

    const [rows] = await pool.promise().query(
      'SELECT * FROM training_courses ORDER BY created_at DESC, id DESC'
    );
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب الدورات التدريبية:', error);
    res.status(500).json({ error: 'فشل في جلب الدورات التدريبية' });
  }
});

// إضافة دورة تدريبية جديدة
router.post('/', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    await createTrainingTable(pool);
    
    const {
      employee_code,
      employee_name,
      department,
      course_name,
      course_date,
      course_duration,
      training_type,
      course_cost,
      notes
    } = req.body;

    // التحقق من صحة البيانات
    if (!employee_code || !employee_name || !department || !course_name || !course_date || !course_duration || !training_type) {
      return res.status(400).json({ error: 'يرجى ملء جميع الحقول المطلوبة' });
    }

    // التحقق من حالة الموظف
    const [employeeCheck] = await pool.promise().query(
      "SELECT status FROM employees WHERE code = ?",
      [employee_code]
    );

    if (employeeCheck.length === 0) {
      return res.status(400).json({ error: 'الموظف غير موجود' });
    }

    if (employeeCheck[0].status === 'مستقيل') {
      return res.status(400).json({ error: 'لا يمكن إضافة دورة تدريبية لموظف مستقيل' });
    }

    const sql = `INSERT INTO training_courses 
      (employee_code, employee_name, department, course_name, course_date, course_duration, training_type, course_cost, notes) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`;
    
    const [result] = await pool.promise().query(sql, [
      employee_code,
      employee_name,
      department,
      course_name,
      course_date,
      parseInt(course_duration),
      training_type,
      parseFloat(course_cost) || 0,
      notes || null
    ]);

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'add',
      module: 'training',
      record_id: result.insertId.toString(),
      message: `تم إضافة دورة تدريبية: ${course_name} للموظف: ${employee_name} (كود: ${employee_code}) بتاريخ: ${course_date} لمدة: ${course_duration} ساعة`
    });

    res.status(201).json({
      message: 'تمت إضافة الدورة التدريبية بنجاح',
      id: result.insertId
    });
  } catch (error) {
    console.error('خطأ في إضافة الدورة التدريبية:', error);
    res.status(500).json({ error: 'فشل في إضافة الدورة التدريبية' });
  }
});

// DataTables server-side processing للدورات التدريبية (يجب أن يأتي قبل /:id)
router.get('/datatables', authenticateToken, addDepartmentFilter, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    await createTrainingTable(pool);

    // معاملات DataTables
    const draw = parseInt(req.query.draw) || 1;
    const start = parseInt(req.query.start) || 0;
    const length = parseInt(req.query.length) || 10;
    const searchValue = req.query.search?.value || '';
    const orderColumnIndex = parseInt(req.query.order?.[0]?.column) || 0;
    const orderDirection = req.query.order?.[0]?.dir || 'desc';

    // معاملات الفلاتر المتقدمة
    const {
      dateFrom,
      dateTo,
      minCost,
      maxCost,
      minDuration,
      maxDuration
    } = req.query;

    // أعمدة الجدول (حسب ترتيب الظهور في HTML)
    const columns = [
      'id',
      'employee_code',
      'employee_name',
      'department',
      'course_name',
      'course_date',
      'course_duration',
      'training_type',
      'course_cost'
    ];

    const orderColumn = columns[orderColumnIndex] || 'id';

    // بناء استعلام البحث مع WHERE clause
    let whereClause = '';
    let searchParams = [];
    let whereConditions = [];

    // إضافة فلترة الإدارات أولاً
    if (req.allowedDepartments === null) {
      // المستخدم admin - لا نطبق فلترة إضافية
      console.log(`👑 Admin user - no department filtering applied to training DataTables`);
    } else if (req.allowedDepartments && req.allowedDepartments.length > 0) {
      // تطبيق فلترة الإدارات المسموحة
      const departmentPlaceholders = req.allowedDepartments.map(() => '?').join(',');
      whereConditions.push(`department IN (${departmentPlaceholders}) AND department IS NOT NULL`);
      searchParams.push(...req.allowedDepartments);
      console.log(`✅ Training DataTables filter applied for departments:`, req.allowedDepartments);
    } else {
      // لا يوجد إدارات مسموحة - لا يعرض أي نتائج
      whereConditions.push(`1 = 0`);
      console.log(`🚫 No departments allowed - blocking all training data in DataTables`);
    }

    // البحث العام
    if (searchValue && searchValue.trim() !== '') {
      whereConditions.push(`(
        employee_code LIKE ? OR
        employee_name LIKE ? OR
        department LIKE ? OR
        course_name LIKE ? OR
        training_type LIKE ? OR
        CAST(course_cost AS CHAR) LIKE ? OR
        CAST(course_duration AS CHAR) LIKE ? OR
        DATE_FORMAT(course_date, '%Y-%m-%d') LIKE ? OR
        notes LIKE ?
      )`);
      const searchPattern = `%${searchValue.trim()}%`;
      searchParams.push(searchPattern, searchPattern, searchPattern, searchPattern, searchPattern, searchPattern, searchPattern, searchPattern, searchPattern);
    }

    // فلاتر البحث في الأعمدة المحددة
    const columnSearches = req.query.columns;
    if (columnSearches) {
      columnSearches.forEach((col, index) => {
        if (col.search && col.search.value && col.search.value.trim() !== '') {
          const columnName = columns[index];
          if (columnName) {
            whereConditions.push(`${columnName} LIKE ?`);
            searchParams.push(`%${col.search.value.trim()}%`);
          }
        }
      });
    }

    // فلاتر التاريخ
    if (dateFrom) {
      whereConditions.push('course_date >= ?');
      searchParams.push(dateFrom);
    }

    if (dateTo) {
      whereConditions.push('course_date <= ?');
      searchParams.push(dateTo);
    }

    // فلاتر التكلفة
    if (minCost && !isNaN(parseFloat(minCost))) {
      whereConditions.push('course_cost >= ?');
      searchParams.push(parseFloat(minCost));
    }

    if (maxCost && !isNaN(parseFloat(maxCost))) {
      whereConditions.push('course_cost <= ?');
      searchParams.push(parseFloat(maxCost));
    }

    // فلاتر مدة الدورة
    if (minDuration && !isNaN(parseInt(minDuration))) {
      whereConditions.push('course_duration >= ?');
      searchParams.push(parseInt(minDuration));
    }

    if (maxDuration && !isNaN(parseInt(maxDuration))) {
      whereConditions.push('course_duration <= ?');
      searchParams.push(parseInt(maxDuration));
    }

    // بناء WHERE clause النهائي
    if (whereConditions.length > 0) {
      whereClause = ' WHERE ' + whereConditions.join(' AND ');
    }

    // حساب إجمالي السجلات مع فلترة الإدارات
    let totalCountWhereClause = '';
    let totalCountParams = [];

    if (req.allowedDepartments === null) {
      // المستخدم admin - عد جميع السجلات
      totalCountWhereClause = '';
      totalCountParams = [];
    } else if (req.allowedDepartments && req.allowedDepartments.length > 0) {
      const departmentPlaceholders = req.allowedDepartments.map(() => '?').join(',');
      totalCountWhereClause = ` WHERE department IN (${departmentPlaceholders}) AND department IS NOT NULL`;
      totalCountParams = [...req.allowedDepartments];
    } else {
      totalCountWhereClause = ' WHERE 1 = 0';
      totalCountParams = [];
    }

    const [totalResult] = await pool.promise().query(
      `SELECT COUNT(*) as total FROM training_courses${totalCountWhereClause}`,
      totalCountParams
    );
    const totalRecords = totalResult[0].total;

    // استعلام العد المفلتر
    const [filteredResult] = await pool.promise().query(
      `SELECT COUNT(*) as total FROM training_courses${whereClause}`,
      searchParams
    );
    const filteredRecords = filteredResult[0].total;

    // استعلام البيانات الرئيسي
    const dataQuery = `
      SELECT id, employee_code, employee_name, department, course_name,
             DATE_FORMAT(course_date, '%Y-%m-%d') as course_date_formatted,
             course_duration, training_type, course_cost, notes
      FROM training_courses
      ${whereClause}
      ORDER BY ${orderColumn} ${orderDirection}
      LIMIT ? OFFSET ?
    `;

    const [rows] = await pool.promise().query(dataQuery, [...searchParams, length, start]);

    // تنسيق البيانات لـ DataTables
    const formattedData = rows.map(row => {
      // تنسيق التاريخ
      const courseDate = row.course_date_formatted || '';

      // تنسيق التكلفة
      const cost = row.course_cost ? `${parseFloat(row.course_cost) % 1 === 0 ? parseInt(row.course_cost) : parseFloat(row.course_cost).toFixed(2)} جنيه` : '0 جنيه';

      // تنسيق مدة الدورة
      const duration = `${row.course_duration || 0} يوم`;

      return [
        row.id || '',
        row.employee_code || '',
        row.employee_name || '',
        row.department || '',
        row.course_name || '',
        courseDate,
        duration,
        row.training_type || '',
        cost,
        `<button class="edit-training-btn btn btn-primary btn-sm" data-training-id="${row.id}" data-permission="edit_training" title="تعديل الدورة">
           <i class="fas fa-edit"></i> تعديل
         </button>
         <button class="delete-training-btn btn btn-danger btn-sm" data-training-id="${row.id}" data-permission="delete_training" title="حذف الدورة">
           <i class="fas fa-trash"></i> حذف
         </button>`
      ];
    });

    // إرجاع البيانات بتنسيق DataTables الصحيح
    const response = {
      draw: parseInt(draw),
      recordsTotal: parseInt(totalRecords),
      recordsFiltered: parseInt(filteredRecords),
      data: formattedData
    };

    res.json(response);

  } catch (error) {
    console.error('خطأ في جلب بيانات DataTables للدورات التدريبية:', error);
    res.status(500).json({
      error: 'فشل في جلب البيانات',
      draw: req.query.draw || 1,
      recordsTotal: 0,
      recordsFiltered: 0,
      data: []
    });
  }
});

// الحصول على دورة تدريبية واحدة بواسطة ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const { id } = req.params;

    const [rows] = await pool.promise().query(
      'SELECT * FROM training_courses WHERE id = ?',
      [id]
    );

    if (rows.length === 0) {
      return res.status(404).json({ error: 'الدورة التدريبية غير موجودة' });
    }

    res.json(rows[0]);
  } catch (error) {
    console.error('خطأ في جلب الدورة التدريبية:', error);
    res.status(500).json({ error: 'فشل في جلب الدورة التدريبية' });
  }
});

// تحديث دورة تدريبية
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const { id } = req.params;
    const {
      employee_code,
      employee_name,
      department,
      course_name,
      course_date,
      course_duration,
      training_type,
      course_cost,
      notes
    } = req.body;

    // التحقق من صحة البيانات
    if (!employee_code || !employee_name || !department || !course_name || !course_date || !course_duration || !training_type) {
      return res.status(400).json({ error: 'يرجى ملء جميع الحقول المطلوبة' });
    }

    // إعداد البيانات للتحديث
    const updateData = {
      employee_code,
      employee_name,
      department,
      course_name,
      course_date,
      course_duration: parseInt(course_duration),
      training_type,
      course_cost: parseFloat(course_cost) || 0,
      notes: notes || null
    };

    // تنظيف البيانات ومعالجة حقول التاريخ
    const cleanedData = cleanUpdateData(updateData);

    // تحضير استعلام التحديث
    const { setClause, values } = prepareUpdateQuery(cleanedData);

    const [result] = await pool.promise().query(
      `UPDATE training_courses SET ${setClause} WHERE id = ?`,
      [...values, id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'الدورة التدريبية غير موجودة' });
    }

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'edit',
      module: 'training',
      record_id: id.toString(),
      message: `تم تعديل دورة تدريبية: ${course_name} للموظف: ${employee_name} (كود: ${employee_code}) بتاريخ: ${course_date} لمدة: ${course_duration} ساعة`
    });

    res.json({ message: 'تم تحديث الدورة التدريبية بنجاح' });
  } catch (error) {
    console.error('خطأ في تحديث الدورة التدريبية:', error);
    res.status(500).json({ error: 'فشل في تحديث الدورة التدريبية' });
  }
});

// حذف دورة تدريبية
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const { id } = req.params;

    // الحصول على معلومات الدورة قبل الحذف
    const [trainingData] = await pool.promise().query(
      'SELECT employee_code, employee_name, course_name, course_date, course_duration FROM training_courses WHERE id = ?',
      [id]
    );

    if (trainingData.length === 0) {
      return res.status(404).json({ error: 'الدورة التدريبية غير موجودة' });
    }

    const training = trainingData[0];

    // حذف الدورة
    const [result] = await pool.promise().query(
      'DELETE FROM training_courses WHERE id = ?',
      [id]
    );

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'delete',
      module: 'training',
      record_id: id.toString(),
      message: `تم حذف دورة تدريبية: ${training.course_name} للموظف: ${training.employee_name} (كود: ${training.employee_code}) - التاريخ: ${training.course_date} - المدة: ${training.course_duration} ساعة`
    });

    res.json({ message: 'تم حذف الدورة التدريبية بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف الدورة التدريبية:', error);
    res.status(500).json({ error: 'فشل في حذف الدورة التدريبية' });
  }
});

// البحث في الدورات التدريبية
router.get('/search', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    await createTrainingTable(pool);
    
    const { employee_code, employee_name, course_name, training_type, department, start_date, end_date } = req.query;
    
    let query = "SELECT * FROM training_courses WHERE 1=1";
    const params = [];
    
    if (employee_code) {
      query += " AND employee_code = ?";
      params.push(employee_code);
    }
    
    if (employee_name) {
      query += " AND employee_name LIKE ?";
      params.push(`%${employee_name}%`);
    }
    
    if (course_name) {
      query += " AND course_name LIKE ?";
      params.push(`%${course_name}%`);
    }
    
    if (training_type) {
      query += " AND training_type = ?";
      params.push(training_type);
    }
    
    if (department) {
      query += " AND department = ?";
      params.push(department);
    }
    
    if (start_date) {
      query += " AND course_date >= ?";
      params.push(start_date);
    }
    
    if (end_date) {
      query += " AND course_date <= ?";
      params.push(end_date);
    }
    
    query += " ORDER BY created_at DESC, id DESC";
    
    const [rows] = await pool.promise().query(query, params);
    res.json(rows);
  } catch (error) {
    console.error('خطأ في البحث عن الدورات التدريبية:', error);
    res.status(500).json({ error: 'فشل في البحث عن الدورات التدريبية' });
  }
});

// إحصائيات التدريب
router.get('/statistics', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    await createTrainingTable(pool);
    
    const { department, training_type, start_date, end_date } = req.query;
    
    let query = "SELECT COUNT(*) as total_courses, SUM(course_cost) as total_cost, AVG(course_duration) as avg_duration FROM training_courses WHERE 1=1";
    const params = [];
    
    if (department) {
      query += " AND department = ?";
      params.push(department);
    }
    
    if (training_type) {
      query += " AND training_type = ?";
      params.push(training_type);
    }
    
    if (start_date) {
      query += " AND course_date >= ?";
      params.push(start_date);
    }
    
    if (end_date) {
      query += " AND course_date <= ?";
      params.push(end_date);
    }
    
    const [rows] = await pool.promise().query(query, params);
    res.json(rows[0]);
  } catch (error) {
    console.error('خطأ في جلب إحصائيات التدريب:', error);
    res.status(500).json({ error: 'فشل في جلب إحصائيات التدريب' });
  }
});

module.exports = router;
