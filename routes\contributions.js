const express = require("express");
const { pool } = require("../config/database");
const { authenticateToken, checkPermission } = require("../middleware/auth");
const { addDepartmentFilter } = require("../middleware/departmentFilter");
const { logAction, createEditMessage } = require('../activityLogger');
// إصلاح خطأ 500 في force-add - تحسين معالجة الأخطاء
// إصلاح مشاكل التحذير السنوي وتسجيل الأنشطة
// إصلاح عرض أنواع المساهمات في سجل الأنشطة

const router = express.Router();

// دالة تحويل نوع المساهمة إلى نص (متطابقة مع HTML)
function getContributionTypeText(contributionType) {
  const types = {
    // القيم النصية القديمة
    'social_insurance': 'التأمين الاجتماعي',
    'medical_insurance': 'التأمين الطبي',
    'pension_fund': 'صندوق المعاشات',
    'savings_fund': 'صندوق الادخار',
    'union_subscription': 'اشتراك النقابة',
    'other': 'أخرى',
    // القيم الرقمية الصحيحة من HTML
    '1': 'الزواج',
    '2': 'إنجاب الطفل',
    '3': 'الولادة الطبيعية للزوجة',
    '4': 'الولادة القيصرية للزوجة',
    '5': 'إصابة العمل',
    '6': 'العمليات الجراحية للعامل الغير مؤمن عليه',
    '7': 'العمليات الجراحية للزوجة والأبناء',
    '8': 'المرضى دون الأمراض المزمنة',
    '9': 'الوفاة المؤمن عليه اجتماعيا',
    '10': 'وفاة الموظف في حالة أنه غير مؤمن عليه اجتماعيا',
    '11': 'وفاة أحد الأقارب من الدرجة الأولى (أحد الوالدين - أو الزوجة - أحد الأبناء)',
    '12': 'زواج أحد أبناء العاملين'
  };
  return types[contributionType] || contributionType;
}

// دالة تنسيق التاريخ
function formatContributionDate(dateString) {
  if (!dateString) return '';

  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  } catch (error) {
    console.error('خطأ في تنسيق التاريخ:', error);
    return dateString;
  }
}

/**
 * دالة لإضافة فلترة الإدارات لاستعلامات المساهمات
 * @param {string} whereClause - شرط WHERE الحالي
 * @param {Array} queryParams - معاملات الاستعلام الحالية
 * @param {Object} req - كائن الطلب
 * @returns {Object} - كائن يحتوي على whereClause و queryParams المحدثة
 */
function addDepartmentFilterToContributions(whereClause, queryParams, req) {
  console.log(`🔍 Contributions Filter - allowedDepartments:`, req.allowedDepartments);

  if (req.allowedDepartments === null) {
    // المستخدم admin - لا نطبق فلترة
    console.log(`👑 Admin user - no filtering applied to contributions`);
    return { whereClause, queryParams };
  } else if (req.allowedDepartments && req.allowedDepartments.length > 0) {
    // تطبيق فلترة الإدارات المسموحة - فلترة على الإدارة من جدول employees فقط
    const departmentPlaceholders = req.allowedDepartments.map(() => '?').join(',');
    // استخدام INNER JOIN بدلاً من LEFT JOIN لضمان وجود موظف مطابق
    whereClause += ` AND e.department IN (${departmentPlaceholders}) AND e.department IS NOT NULL`;
    queryParams.push(...req.allowedDepartments);
    console.log(`✅ Contributions filter applied for departments:`, req.allowedDepartments);
    console.log(`📝 Final WHERE clause:`, whereClause);
    return { whereClause, queryParams };
  } else {
    // لا يوجد إدارات مسموحة - لا يعرض أي نتائج
    whereClause += ` AND 1 = 0`;
    console.log(`🚫 No departments allowed - blocking all contributions data`);
    console.log(`📝 Final WHERE clause:`, whereClause);
    return { whereClause, queryParams };
  }
}

// متغير لتتبع ما إذا تم إنشاء الجدول أم لا
let contributionsTableCreated = false;

// إنشاء جدول المساهمات إذا لم يكن موجودًا
const createContributionsTable = async () => {
  if (contributionsTableCreated) return; // إذا تم إنشاء الجدول بالفعل، لا تفعل شيئاً
  try {
    await pool.promise().query(`
      CREATE TABLE IF NOT EXISTS contributions (
        id int NOT NULL AUTO_INCREMENT,
        employee_code varchar(50) NOT NULL,
        employee_name varchar(255) NOT NULL,
        contribution_type varchar(50) NOT NULL,
        company_amount decimal(10,2) NOT NULL,
        fund_amount decimal(10,2) NOT NULL,
        contribution_date date NOT NULL,
        notes text,
        created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        status varchar(50) DEFAULT 'active',
        PRIMARY KEY (id),
        KEY idx_employee_code (employee_code),
        KEY idx_contribution_type (contribution_type),
        KEY idx_contribution_date (contribution_date)
      ) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
    `);

    contributionsTableCreated = true; // تعيين المتغير إلى true بعد إنشاء الجدول
  } catch (error) {
    console.error('خطأ في إنشاء جدول المساهمات:', error);
    throw error;
  }
};

// إنشاء جدول المساهمات
router.get('/create-contributions-table', authenticateToken, checkPermission('can_add'), async (req, res) => {
  try {
    await createContributionsTable();
    res.json({ message: 'تم إنشاء جدول المساهمات بنجاح' });
  } catch (error) {
    console.error('خطأ في إنشاء جدول المساهمات:', error);
    res.status(500).json({ error: 'فشل في إنشاء جدول المساهمات' });
  }
});

// الحصول على جميع المساهمات
router.get('/', authenticateToken, addDepartmentFilter, checkPermission('view_contributions'), async (req, res) => {
  try {
    // بناء الاستعلام مع فلترة الإدارات
    let query, params;

    if (req.allowedDepartments === null) {
      // المستخدم admin - جلب جميع المساهمات
      query = `
        SELECT c.*, e.full_name as employee_name, e.department
        FROM contributions c
        LEFT JOIN employees e ON c.employee_code = e.code
        ORDER BY c.created_at DESC, c.id DESC
      `;
      params = [];
      console.log(`👑 Admin user - fetching all contributions`);
    } else if (req.allowedDepartments && req.allowedDepartments.length > 0) {
      // المستخدم محدود - فلترة حسب الإدارات المسموحة
      const departmentPlaceholders = req.allowedDepartments.map(() => '?').join(',');
      query = `
        SELECT c.*, e.full_name as employee_name, e.department
        FROM contributions c
        INNER JOIN employees e ON c.employee_code = e.code
        WHERE e.department IN (${departmentPlaceholders}) AND e.department IS NOT NULL
        ORDER BY c.created_at DESC, c.id DESC
      `;
      params = req.allowedDepartments;
      console.log(`🔒 Limited user - filtering contributions for departments:`, req.allowedDepartments);
    } else {
      // لا يوجد إدارات مسموحة
      query = `SELECT * FROM contributions WHERE 1 = 0`;
      params = [];
      console.log(`🚫 No departments allowed - blocking all contributions`);
    }

    const [rows] = await pool.promise().query(query, params);
    console.log(`📊 Contributions fetched: ${rows.length} records`);
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب المساهمات:', error);
    res.status(500).json({ error: 'فشل في جلب المساهمات' });
  }
});

// البحث في المساهمات
router.get('/search', authenticateToken, checkPermission('view_contributions'), async (req, res) => {
  try {
    const {
      employee_code,
      employee_name,
      contribution_type,
      start_date,
      end_date,
      min_amount,
      max_amount,
      status
    } = req.query;

    let query = `
      SELECT c.*, e.full_name as employee_name, e.department
      FROM contributions c
      LEFT JOIN employees e ON c.employee_code = e.code
      WHERE 1=1
    `;
    const params = [];

    if (employee_code) {
      query += " AND c.employee_code = ?";
      params.push(employee_code);
    }

    if (employee_name) {
      query += " AND e.full_name LIKE ?";
      params.push(`%${employee_name}%`);
    }
    
    if (contribution_type) {
      query += " AND c.contribution_type = ?";
      params.push(contribution_type);
    }
    
    if (start_date) {
      query += " AND c.contribution_date >= ?";
      params.push(start_date);
    }

    if (end_date) {
      query += " AND c.contribution_date <= ?";
      params.push(end_date);
    }
    
    if (min_amount) {
      query += " AND c.amount >= ?";
      params.push(min_amount);
    }
    
    if (max_amount) {
      query += " AND c.amount <= ?";
      params.push(max_amount);
    }
    
    if (status) {
      query += " AND c.status = ?";
      params.push(status);
    }
    
    query += " ORDER BY c.created_at DESC, c.id DESC";
    
    const [rows] = await pool.promise().query(query, params);
    res.json(rows);
  } catch (error) {
    console.error('خطأ في البحث في المساهمات:', error);
    res.status(500).json({ error: 'فشل في البحث في المساهمات' });
  }
});

// إضافة مساهمة جديدة
router.post('/', authenticateToken, checkPermission('can_add'), async (req, res) => {
  try {


    await createContributionsTable();

    const {
      employee_code,
      employee_name,
      contribution_type,
      company_amount,
      fund_amount,
      contribution_date,
      notes,
      status = 'active'
    } = req.body;



    // التحقق من الحقول الأساسية المطلوبة
    if (!employee_code || !contribution_type || !contribution_date) {
      return res.status(400).json({ error: 'الحقول المطلوبة: كود الموظف، نوع المساهمة، تاريخ المساهمة' });
    }

    // التحقق من وجود مساهمة واحدة على الأقل
    const hasCompanyAmount = company_amount && parseFloat(company_amount) > 0;
    const hasFundAmount = fund_amount && parseFloat(fund_amount) > 0;

    if (!hasCompanyAmount && !hasFundAmount) {
      return res.status(400).json({ error: 'يجب إدخال مبلغ مساهمة واحد على الأقل (الشركة أو صندوق الزمالة)' });
    }

    // التحقق من عدم وجود مساهمة مكررة لنفس الموظف ونفس النوع في نفس التاريخ
    const [existingContributions] = await pool.promise().query(
      `SELECT id, employee_name, contribution_type, contribution_date
       FROM contributions
       WHERE employee_code = ? AND contribution_type = ? AND contribution_date = ?`,
      [employee_code, contribution_type, contribution_date]
    );

    if (existingContributions.length > 0) {
      const existing = existingContributions[0];
      return res.status(400).json({
        error: `يوجد مساهمة مماثلة للموظف ${employee_name || existing.employee_name} من نوع "${contribution_type}" في تاريخ ${existing.contribution_date}. لا يمكن إضافة مساهمات مكررة.`,
        existing_contribution: {
          id: existing.id,
          type: existing.contribution_type,
          contribution_date: existing.contribution_date
        }
      });
    }

    // فحص المساهمات السنوية - التحقق من وجود مساهمة من نفس النوع خلال السنة الماضية
    const oneYearAgo = new Date(contribution_date);
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
    const oneYearAgoStr = oneYearAgo.toISOString().split('T')[0];

    console.log('🔍 فحص المساهمات السنوية:');
    console.log('كود الموظف:', employee_code);
    console.log('نوع المساهمة المطلوب:', contribution_type);
    console.log('تاريخ المساهمة الجديدة:', contribution_date);
    console.log('تاريخ السنة الماضية:', oneYearAgoStr);

    const [yearlyContributions] = await pool.promise().query(
      `SELECT id, employee_name, contribution_type, contribution_date,
              DATEDIFF(?, contribution_date) as days_since_last
       FROM contributions
       WHERE employee_code = ? AND contribution_type = ?
       AND contribution_date > ?
       ORDER BY contribution_date DESC LIMIT 1`,
      [contribution_date, employee_code, contribution_type, oneYearAgoStr]
    );

    console.log('📊 نتائج البحث السنوي:', yearlyContributions.length, 'مساهمة');
    if (yearlyContributions.length > 0) {
      console.log('📋 المساهمة الموجودة:', yearlyContributions[0]);
    }

    // للتشخيص: فحص جميع مساهمات الموظف
    const [allContributions] = await pool.promise().query(
      `SELECT id, contribution_type, contribution_date, company_amount, fund_amount
       FROM contributions
       WHERE employee_code = ?
       ORDER BY contribution_date DESC LIMIT 5`,
      [employee_code]
    );
    console.log('📋 جميع مساهمات الموظف (آخر 5):', allContributions);

    if (yearlyContributions.length > 0) {
      const lastContribution = yearlyContributions[0];
      const daysSince = lastContribution.days_since_last;

      // تحويل نوع المساهمة إلى نص
      const contributionTypeText = getContributionTypeText(contribution_type);

      // تنسيق التاريخ
      const formattedDate = formatContributionDate(lastContribution.contribution_date);

      return res.status(409).json({
        error: `تنبيه: تم إضافة مساهمة من نوع "${contributionTypeText}" لهذا الموظف منذ ${daysSince} يوم فقط (${formattedDate}). هل تريد المتابعة؟`,
        warning: true,
        yearly_check: true,
        last_contribution: {
          id: lastContribution.id,
          type: lastContribution.contribution_type,
          type_text: contributionTypeText,
          date: lastContribution.contribution_date,
          formatted_date: formattedDate,
          days_since: daysSince
        }
      });
    }
    
    // الحصول على اسم الموظف إذا لم يتم إرساله
    let finalEmployeeName = employee_name;
    if (!finalEmployeeName) {
      const [employeeRows] = await pool.promise().query(
        "SELECT name FROM employees WHERE employee_code = ?",
        [employee_code]
      );
      finalEmployeeName = employeeRows.length > 0 ? employeeRows[0].name : null;
    }

    // تحويل القيم الفارغة إلى 0
    const finalCompanyAmount = company_amount ? parseFloat(company_amount) : 0;
    const finalFundAmount = fund_amount ? parseFloat(fund_amount) : 0;

    const [result] = await pool.promise().query(
      `INSERT INTO contributions (
        employee_code, employee_name, contribution_type, company_amount, fund_amount,
        contribution_date, notes, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        employee_code, finalEmployeeName, contribution_type, finalCompanyAmount, finalFundAmount,
        contribution_date, notes, status
      ]
    );
    
    // تسجيل النشاط
    const contributionTypeText = getContributionTypeText(contribution_type);
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'add',
      module: 'contributions',
      record_id: result.insertId.toString(),
      message: `تم إضافة مساهمة للموظف: ${finalEmployeeName} (كود: ${employee_code}) نوع: ${contributionTypeText} - مساهمة الشركة: ${company_amount} جنيه - مساهمة صندوق الزمالة: ${fund_amount} جنيه`
    });

    res.status(201).json({
      id: result.insertId,
      employee_code,
      employee_name: finalEmployeeName,
      contribution_type,
      company_amount,
      fund_amount,
      contribution_date,
      notes,
      status,
      message: 'تم إضافة المساهمة بنجاح'
    });
  } catch (error) {
    console.error('خطأ في إضافة المساهمة:', error);
    res.status(500).json({ error: 'فشل في إضافة المساهمة' });
  }
});

// تحديث مساهمة
router.put('/:id', authenticateToken, checkPermission('can_edit'), async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;



    // التحقق من وجود المساهمة والحصول على البيانات القديمة
    const [existingContribution] = await pool.promise().query(
      "SELECT * FROM contributions WHERE id = ?",
      [id]
    );

    if (existingContribution.length === 0) {
      return res.status(404).json({ error: 'المساهمة غير موجودة' });
    }

    const oldData = existingContribution[0];

    // التحقق من عدم وجود مساهمة مكررة (إذا تم تغيير البيانات الأساسية)
    if (updateData.employee_code || updateData.contribution_type || updateData.contribution_date) {
      const checkEmployeeCode = updateData.employee_code || oldData.employee_code;
      const checkContributionType = updateData.contribution_type || oldData.contribution_type;
      const checkContributionDate = updateData.contribution_date || oldData.contribution_date;

      const [duplicateContributions] = await pool.promise().query(
        `SELECT id, employee_name, contribution_type, contribution_date
         FROM contributions
         WHERE employee_code = ? AND contribution_type = ? AND contribution_date = ? AND id != ?`,
        [checkEmployeeCode, checkContributionType, checkContributionDate, id]
      );

      if (duplicateContributions.length > 0) {
        const existing = duplicateContributions[0];
        return res.status(400).json({
          error: `يوجد مساهمة مماثلة للموظف ${existing.employee_name} من نوع "${checkContributionType}" في تاريخ ${existing.contribution_date}. لا يمكن تحديث المساهمة لتصبح مكررة.`,
          existing_contribution: {
            id: existing.id,
            type: existing.contribution_type,
            contribution_date: existing.contribution_date
          }
        });
      }
    }

    // التحقق من وجود مساهمة واحدة على الأقل (إذا تم تحديث المبالغ)
    if (updateData.hasOwnProperty('company_amount') || updateData.hasOwnProperty('fund_amount')) {
      const finalCompanyAmount = updateData.hasOwnProperty('company_amount') ?
        (updateData.company_amount ? parseFloat(updateData.company_amount) : 0) :
        parseFloat(oldData.company_amount);

      const finalFundAmount = updateData.hasOwnProperty('fund_amount') ?
        (updateData.fund_amount ? parseFloat(updateData.fund_amount) : 0) :
        parseFloat(oldData.fund_amount);

      if (finalCompanyAmount <= 0 && finalFundAmount <= 0) {
        return res.status(400).json({ error: 'يجب أن يكون هناك مبلغ مساهمة واحد على الأقل (الشركة أو صندوق الزمالة)' });
      }

      // تحديث القيم في updateData
      if (updateData.hasOwnProperty('company_amount')) {
        updateData.company_amount = finalCompanyAmount;
      }
      if (updateData.hasOwnProperty('fund_amount')) {
        updateData.fund_amount = finalFundAmount;
      }
    }

    // إزالة الحقول غير المسموح بتحديثها
    delete updateData.id;
    delete updateData.created_at;
    delete updateData.updated_at; // يتم تحديثه تلقائياً في قاعدة البيانات

    // إذا تم تغيير كود الموظف، تحديث اسم الموظف
    if (updateData.employee_code && updateData.employee_code !== existingContribution[0].employee_code) {
      const [employeeRows] = await pool.promise().query(
        "SELECT name FROM employees WHERE employee_code = ?",
        [updateData.employee_code]
      );

      updateData.employee_name = employeeRows.length > 0 ? employeeRows[0].name : null;
    }

    // قائمة حقول التاريخ التي تحتاج معالجة خاصة
    const dateFields = [
      'contribution_date', 'start_date', 'end_date'
    ];

    // معالجة حقول التاريخ - تحويل القيم الفارغة إلى null
    dateFields.forEach(field => {
      if (updateData.hasOwnProperty(field)) {
        if (updateData[field] === '' || updateData[field] === null || updateData[field] === undefined) {
          updateData[field] = null;
        }
      }
    });

    // تحضير البيانات للتحديث
    const fields = Object.keys(updateData);
    const values = Object.values(updateData);

    if (fields.length === 0) {
      return res.status(400).json({ error: 'لم يتم توفير أي بيانات للتحديث' });
    }

    const setClause = fields.map(field => `${field} = ?`).join(', ');
    values.push(id);

    await pool.promise().query(
      `UPDATE contributions SET ${setClause} WHERE id = ?`,
      values
    );

    // إنشاء البيانات الجديدة للمقارنة
    const newData = { ...oldData, ...updateData };

    // تحويل أنواع المساهمات إلى نص للعرض في سجل الأنشطة
    const processedOldData = { ...oldData };
    const processedNewData = { ...newData };

    if (processedOldData.contribution_type) {
      processedOldData.contribution_type_display = getContributionTypeText(processedOldData.contribution_type);
    }
    if (processedNewData.contribution_type) {
      processedNewData.contribution_type_display = getContributionTypeText(processedNewData.contribution_type);
    }

    // إنشاء رسالة التعديل المفصلة
    const fieldLabels = {
      employee_code: 'كود الموظف',
      employee_name: 'اسم الموظف',
      contribution_type_display: 'نوع المساهمة',
      company_amount: 'مساهمة الشركة',
      fund_amount: 'مساهمة صندوق الزمالة',
      contribution_date: 'تاريخ المساهمة',
      notes: 'الملاحظات',
      status: 'الحالة'
    };

    const editMessage = createEditMessage(
      `مساهمة للموظف: ${newData.employee_name || newData.employee_code}`,
      processedOldData,
      processedNewData,
      fieldLabels
    );

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'edit',
      module: 'contributions',
      record_id: id.toString(),
      message: editMessage
    });

    res.json({ message: 'تم تحديث المساهمة بنجاح' });
  } catch (error) {
    console.error('خطأ في تحديث المساهمة:', error);
    res.status(500).json({ error: 'فشل في تحديث المساهمة' });
  }
});

// حذف مساهمة
router.delete('/:id', authenticateToken, checkPermission('can_delete'), async (req, res) => {
  try {
    const { id } = req.params;

    // الحصول على معلومات المساهمة قبل الحذف
    const [contributionData] = await pool.promise().query(
      "SELECT employee_code, employee_name, contribution_type, company_amount, fund_amount FROM contributions WHERE id = ?",
      [id]
    );

    if (contributionData.length === 0) {
      return res.status(404).json({ error: 'المساهمة غير موجودة' });
    }

    const contribution = contributionData[0];

    // حذف المساهمة
    const [result] = await pool.promise().query(
      "DELETE FROM contributions WHERE id = ?",
      [id]
    );

    // تسجيل النشاط
    const contributionTypeText = getContributionTypeText(contribution.contribution_type);
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'delete',
      module: 'contributions',
      record_id: id.toString(),
      message: `تم حذف مساهمة للموظف: ${contribution.employee_name} (كود: ${contribution.employee_code}) نوع: ${contributionTypeText} - مساهمة الشركة: ${contribution.company_amount} جنيه - مساهمة صندوق الزمالة: ${contribution.fund_amount} جنيه`
    });

    res.json({ message: 'تم حذف المساهمة بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف المساهمة:', error);
    res.status(500).json({ error: 'فشل في حذف المساهمة' });
  }
});

// الحصول على مساهمات موظف محدد
router.get('/employee/:employee_code', authenticateToken, checkPermission('view_contributions'), async (req, res) => {
  try {
    const { employee_code } = req.params;
    
    const [rows] = await pool.promise().query(`
      SELECT c.*, e.full_name as employee_name, e.department
      FROM contributions c
      LEFT JOIN employees e ON c.employee_code = e.code
      WHERE c.employee_code = ?
      ORDER BY c.created_at DESC, c.id DESC
    `, [employee_code]);
    
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب مساهمات الموظف:', error);
    res.status(500).json({ error: 'فشل في جلب مساهمات الموظف' });
  }
});

// الحصول على إحصائيات المساهمات
router.get('/statistics', authenticateToken, checkPermission('view_contributions'), async (req, res) => {
  try {
    const { start_date, end_date } = req.query;
    
    let dateFilter = "";
    const params = [];
    
    if (start_date && end_date) {
      dateFilter = " WHERE contribution_date BETWEEN ? AND ?";
      params.push(start_date, end_date);
    }
    
    // إجمالي المساهمات
    const [totalResult] = await pool.promise().query(
      `SELECT COUNT(*) as total_count, SUM(company_amount + fund_amount) as total_amount FROM contributions${dateFilter}`,
      params
    );

    // المساهمات حسب النوع
    const [typeResult] = await pool.promise().query(
      `SELECT contribution_type, COUNT(*) as count, SUM(company_amount + fund_amount) as total_amount
       FROM contributions${dateFilter}
       GROUP BY contribution_type
       ORDER BY total_amount DESC`,
      params
    );
    
    // أعلى المساهمين
    const [topContributors] = await pool.promise().query(
      `SELECT c.employee_code, c.employee_name, COUNT(*) as contribution_count, SUM(c.company_amount + c.fund_amount) as total_amount
       FROM contributions c${dateFilter}
       GROUP BY c.employee_code, c.employee_name
       ORDER BY total_amount DESC
       LIMIT 10`,
      params
    );
    
    res.json({
      total: totalResult[0],
      by_type: typeResult,
      top_contributors: topContributors
    });
  } catch (error) {
    console.error('خطأ في جلب إحصائيات المساهمات:', error);
    res.status(500).json({ error: 'فشل في جلب إحصائيات المساهمات' });
  }
});

// إضافة مساهمة مع تجاهل التحذير السنوي
router.post('/force-add', authenticateToken, async (req, res) => {
  try {
    console.log('🚀 بدء معالجة طلب force-add');
    const pool = req.app.locals.pool;

    if (!pool) {
      console.error('❌ pool غير متاح');
      return res.status(500).json({ error: 'خطأ في الاتصال بقاعدة البيانات' });
    }

    await createContributionsTable();

    const {
      employee_code,
      employee_name,
      contribution_type,
      company_amount,
      fund_amount,
      contribution_date,
      notes,
      status = 'active',
      ignore_yearly_warning = false
    } = req.body;

    // تسجيل البيانات المستلمة للتشخيص
    console.log('📥 بيانات force-add المستلمة:', {
      employee_code, employee_name, contribution_type,
      company_amount, fund_amount, contribution_date, notes
    });

    // التحقق من الحقول المطلوبة مع تفاصيل أكثر
    const missingFields = [];
    if (!employee_code) missingFields.push('employee_code');
    if (!contribution_type) missingFields.push('contribution_type');
    if (company_amount === undefined || company_amount === null) missingFields.push('company_amount');
    if (fund_amount === undefined || fund_amount === null) missingFields.push('fund_amount');
    if (!contribution_date) missingFields.push('contribution_date');

    if (missingFields.length > 0) {
      console.error('❌ حقول مفقودة في force-add:', missingFields);
      return res.status(400).json({
        error: `جميع الحقول المطلوبة يجب أن تكون موجودة. الحقول المفقودة: ${missingFields.join(', ')}`
      });
    }

    // التحقق من صحة القيم الرقمية
    const companyAmountNum = parseFloat(company_amount);
    const fundAmountNum = parseFloat(fund_amount);

    if (isNaN(companyAmountNum) || companyAmountNum < 0) {
      console.error('❌ قيمة مساهمة الشركة غير صحيحة:', company_amount);
      return res.status(400).json({ error: 'قيمة مساهمة الشركة يجب أن تكون رقم صحيح وأكبر من أو يساوي الصفر' });
    }

    if (isNaN(fundAmountNum) || fundAmountNum < 0) {
      console.error('❌ قيمة مساهمة الصندوق غير صحيحة:', fund_amount);
      return res.status(400).json({ error: 'قيمة مساهمة الصندوق يجب أن تكون رقم صحيح وأكبر من أو يساوي الصفر' });
    }

    // فحص التكرار في نفس التاريخ فقط (تجاهل التحذير السنوي)
    const [existingContributions] = await pool.promise().query(
      `SELECT id, employee_name, contribution_type, contribution_date
       FROM contributions
       WHERE employee_code = ? AND contribution_type = ? AND contribution_date = ?`,
      [employee_code, contribution_type, contribution_date]
    );

    if (existingContributions.length > 0) {
      const existing = existingContributions[0];
      return res.status(400).json({
        error: `يوجد مساهمة مماثلة للموظف ${employee_name || existing.employee_name} من نوع "${contribution_type}" في تاريخ ${existing.contribution_date}. لا يمكن إضافة مساهمات مكررة.`
      });
    }

    // تحويل القيم الفارغة إلى 0
    const finalCompanyAmount = company_amount ? parseFloat(company_amount) : 0;
    const finalFundAmount = fund_amount ? parseFloat(fund_amount) : 0;

    // إدراج المساهمة الجديدة
    console.log('📝 إدراج المساهمة في قاعدة البيانات:', {
      employee_code, employee_name, contribution_type,
      finalCompanyAmount, finalFundAmount, contribution_date, notes, status
    });

    const [result] = await pool.promise().query(
      `INSERT INTO contributions (
        employee_code, employee_name, contribution_type, company_amount,
        fund_amount, contribution_date, notes, status, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())`,
      [employee_code, employee_name, contribution_type, finalCompanyAmount, finalFundAmount, contribution_date, notes, status]
    );

    console.log('✅ تم إدراج المساهمة بنجاح، ID:', result.insertId);

    // تسجيل النشاط
    const username = req.user?.username || 'مجهول';
    const contributionTypeText = getContributionTypeText(contribution_type);
    try {
      await logAction({
        username: username,
        action_type: 'add',
        module: 'contributions',
        record_id: result.insertId,
        message: `إضافة مساهمة قسرية للموظف ${employee_code} - ${employee_name} (نوع: ${contributionTypeText})`
      });
      console.log('✅ تم تسجيل النشاط بنجاح');
    } catch (logError) {
      console.error('❌ خطأ في تسجيل النشاط:', logError);
      // لا نوقف العملية بسبب خطأ في التسجيل
    }

    res.status(201).json({
      message: 'تم إضافة المساهمة بنجاح',
      id: result.insertId,
      forced: true
    });

  } catch (error) {
    console.error('❌ خطأ في إضافة المساهمة القسرية:', error);
    console.error('📋 تفاصيل الخطأ:', {
      message: error.message,
      code: error.code,
      sqlState: error.sqlState,
      sqlMessage: error.sqlMessage
    });

    // رسالة خطأ أكثر تفصيلاً
    let errorMessage = 'فشل في إضافة المساهمة';
    if (error.code === 'ER_NO_SUCH_TABLE') {
      errorMessage = 'جدول المساهمات غير موجود';
    } else if (error.code === 'ER_DUP_ENTRY') {
      errorMessage = 'مساهمة مكررة';
    } else if (error.code === 'ER_BAD_FIELD_ERROR') {
      errorMessage = 'خطأ في بنية الجدول';
    }

    res.status(500).json({
      error: errorMessage,
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// DataTables server-side processing للمساهمات المضافة
router.get('/datatables', authenticateToken, addDepartmentFilter, checkPermission('view_contributions'), async (req, res) => {
  try {
    const pool = req.app.locals.pool;

    // التأكد من وجود الجدول
    try {
      await createContributionsTable();
    } catch (tableError) {
      console.error('Error creating table:', tableError);
    }

    // معاملات DataTables
    const draw = parseInt(req.query.draw) || 1;
    const start = parseInt(req.query.start) || 0;
    const length = parseInt(req.query.length) || 10;
    const searchValue = req.query.search?.value || '';
    const orderColumnIndex = parseInt(req.query.order?.[0]?.column) || 0;
    const orderDirection = req.query.order?.[0]?.dir || 'desc';

    // أعمدة الجدول (حسب ترتيب الظهور في HTML)
    const columns = [
      'id',
      'employee_code',
      'employee_name',
      'department',
      'contribution_type',
      'company_amount',
      'fund_amount',
      'contribution_date',
      'notes'
    ];

    const orderColumn = columns[orderColumnIndex] || 'id';

    // معاملات البحث المتقدم
    const contributionId = req.query.contributionId || '';
    const employeeCode = req.query.employeeCode || '';
    const employeeName = req.query.employeeName || '';
    const department = req.query.department || '';
    const contributionType = req.query.contributionType || '';
    const minAmount = req.query.minAmount || '';
    const maxAmount = req.query.maxAmount || '';
    const dateFrom = req.query.dateFrom || '';
    const dateTo = req.query.dateTo || '';

    // بناء استعلام العد الإجمالي مع فلترة الإدارات
    let totalQuery, totalParams;

    if (req.allowedDepartments === null) {
      // المستخدم admin - عد جميع المساهمات
      totalQuery = `
        SELECT COUNT(*) as total
        FROM contributions c
        LEFT JOIN employees e ON c.employee_code = e.code
      `;
      totalParams = [];
    } else if (req.allowedDepartments && req.allowedDepartments.length > 0) {
      // المستخدم محدود - عد المساهمات للإدارات المسموحة فقط
      const departmentPlaceholders = req.allowedDepartments.map(() => '?').join(',');
      totalQuery = `
        SELECT COUNT(*) as total
        FROM contributions c
        INNER JOIN employees e ON c.employee_code = e.code
        WHERE e.department IN (${departmentPlaceholders}) AND e.department IS NOT NULL
      `;
      totalParams = req.allowedDepartments;
    } else {
      // لا يوجد إدارات مسموحة
      totalQuery = `SELECT 0 as total`;
      totalParams = [];
    }

    const [totalResult] = await pool.promise().query(totalQuery, totalParams);
    const totalRecords = totalResult[0].total;

    // بناء استعلام البحث مع فلترة الإدارات
    let searchQuery, searchParams;

    if (req.allowedDepartments === null) {
      // المستخدم admin - جلب جميع المساهمات
      searchQuery = `
        SELECT
          c.id,
          c.employee_code,
          CASE
            WHEN e.full_name IS NOT NULL THEN e.full_name
            ELSE c.employee_name
          END as employee_name,
          CASE
            WHEN e.department IS NOT NULL THEN e.department
            ELSE 'غير محدد'
          END as department,
          c.contribution_type,
          c.company_amount,
          c.fund_amount,
          DATE_FORMAT(c.contribution_date, '%Y-%m-%d') as contribution_date_formatted,
          c.notes
        FROM contributions c
        LEFT JOIN employees e ON c.employee_code = e.code
        WHERE 1=1
      `;
      searchParams = [];
    } else if (req.allowedDepartments && req.allowedDepartments.length > 0) {
      // المستخدم محدود - فلترة حسب الإدارات المسموحة
      const departmentPlaceholders = req.allowedDepartments.map(() => '?').join(',');
      searchQuery = `
        SELECT
          c.id,
          c.employee_code,
          e.full_name as employee_name,
          e.department,
          c.contribution_type,
          c.company_amount,
          c.fund_amount,
          DATE_FORMAT(c.contribution_date, '%Y-%m-%d') as contribution_date_formatted,
          c.notes
        FROM contributions c
        INNER JOIN employees e ON c.employee_code = e.code
        WHERE e.department IN (${departmentPlaceholders}) AND e.department IS NOT NULL
      `;
      searchParams = [...req.allowedDepartments];
    } else {
      // لا يوجد إدارات مسموحة - لا يعرض أي نتائج
      searchQuery = `
        SELECT
          c.id,
          c.employee_code,
          '' as employee_name,
          '' as department,
          c.contribution_type,
          c.company_amount,
          c.fund_amount,
          DATE_FORMAT(c.contribution_date, '%Y-%m-%d') as contribution_date_formatted,
          c.notes
        FROM contributions c
        WHERE 1 = 0
      `;
      searchParams = [];
    }

    // البحث العام في DataTables
    if (searchValue) {
      searchQuery += ` AND (
        c.id LIKE ? OR
        c.employee_code LIKE ? OR
        c.employee_name LIKE ? OR
        e.full_name LIKE ? OR
        e.department LIKE ? OR
        c.contribution_type LIKE ? OR
        c.notes LIKE ?
      )`;
      const searchPattern = `%${searchValue}%`;
      searchParams.push(searchPattern, searchPattern, searchPattern, searchPattern, searchPattern, searchPattern, searchPattern);
    }

    // البحث المتقدم
    if (contributionId) {
      searchQuery += " AND c.id = ?";
      searchParams.push(contributionId);
    }

    if (employeeCode) {
      searchQuery += " AND c.employee_code LIKE ?";
      searchParams.push(`%${employeeCode}%`);
    }

    if (employeeName) {
      searchQuery += " AND (c.employee_name LIKE ? OR e.full_name LIKE ?)";
      searchParams.push(`%${employeeName}%`, `%${employeeName}%`);
    }

    if (department) {
      searchQuery += " AND e.department LIKE ?";
      searchParams.push(`%${department}%`);
    }

    if (contributionType) {
      searchQuery += " AND c.contribution_type = ?";
      searchParams.push(contributionType);
    }

    if (minAmount) {
      searchQuery += " AND (c.company_amount >= ? OR c.fund_amount >= ?)";
      searchParams.push(minAmount, minAmount);
    }

    if (maxAmount) {
      searchQuery += " AND (c.company_amount <= ? OR c.fund_amount <= ?)";
      searchParams.push(maxAmount, maxAmount);
    }

    if (dateFrom) {
      searchQuery += " AND c.contribution_date >= ?";
      searchParams.push(dateFrom);
    }

    if (dateTo) {
      searchQuery += " AND c.contribution_date <= ?";
      searchParams.push(dateTo);
    }

    // حساب العدد المفلتر
    let countQuery = searchQuery.replace(/SELECT[\s\S]*?FROM/, 'SELECT COUNT(*) as filtered FROM');
    const [countResult] = await pool.promise().query(countQuery, searchParams);
    const filteredRecords = countResult[0].filtered;

    // إضافة الترتيب والحد
    searchQuery += ` ORDER BY ${orderColumn} ${orderDirection} LIMIT ? OFFSET ?`;
    searchParams.push(length, start);

    console.log('Final search query:', searchQuery);
    console.log('Search params:', searchParams);

    const [rows] = await pool.promise().query(searchQuery, searchParams);
    console.log('Query results count:', rows.length);

    // تنسيق البيانات لـ DataTables
    const formattedData = rows.map(row => {
      // تحويل نوع المساهمة إلى نص
      const contributionTypeNames = {
        '1': 'الزواج',
        '2': 'إنجاب الطفل',
        '3': 'الولادة الطبيعية للزوجة',
        '4': 'الولادة القيصرية للزوجة',
        '5': 'إصابة العمل',
        '6': 'العمليات الجراحية للعامل الغير مؤمن عليه',
        '7': 'العمليات الجراحية للزوجة والأبناء',
        '8': 'المرضى دون الأمراض المزمنة',
        '9': 'الوفاة المؤمن عليه اجتماعيا',
        '10': 'وفاة الموظف في حالة أنه غير مؤمن عليه اجتماعيا',
        '11': 'وفاة أحد الأقارب من الدرجة الأولى',
        '12': 'زواج أحد أبناء العاملين'
      };

      return [
        row.id,
        row.employee_code,
        row.employee_name,
        row.department,
        contributionTypeNames[row.contribution_type] || row.contribution_type,
        parseFloat(row.company_amount) % 1 === 0 ? parseInt(row.company_amount) : parseFloat(row.company_amount).toFixed(2),
        parseFloat(row.fund_amount) % 1 === 0 ? parseInt(row.fund_amount) : parseFloat(row.fund_amount).toFixed(2),
        row.contribution_date_formatted,
        row.notes || '-',
        `<button class="edit-added-btn" data-contribution-id="${row.id}" data-permission="can_edit">تعديل</button>
         <button class="delete-added-btn" data-contribution-id="${row.id}" data-permission="can_delete">حذف</button>`
      ];
    });

    // إرجاع البيانات بتنسيق DataTables
    res.json({
      draw: draw,
      recordsTotal: totalRecords,
      recordsFiltered: filteredRecords,
      data: formattedData
    });

  } catch (error) {
    console.error('خطأ في جلب بيانات DataTables:', error);
    console.error('Stack trace:', error.stack);
    console.error('Request query:', req.query);
    res.status(500).json({
      error: 'فشل في جلب البيانات',
      message: error.message,
      draw: req.query.draw || 1,
      recordsTotal: 0,
      recordsFiltered: 0,
      data: []
    });
  }
});

module.exports = router;