const express = require('express');
const { pool } = require("../config/database");
const { authenticateToken, checkPermission } = require("../middleware/auth");
const { addDepartmentFilter } = require("../middleware/departmentFilter");
const { logAction, createEditMessage } = require('../activityLogger');
const { cleanUpdateData, cleanInsertData, prepareUpdateQuery, prepareInsertQuery } = require('../utils/dataCleanup');

const router = express.Router();

console.log('تم تحميل ملف routes/rewards-deductions.js');

/**
 * دالة لإضافة فلترة الإدارات لاستعلامات المكافآت والخصومات
 * @param {string} whereClause - شرط WHERE الحالي
 * @param {Array} queryParams - معاملات الاستعلام الحالية
 * @param {Object} req - كائن الطلب
 * @param {string} tableAlias - اسم الجدول أو alias (r للمكافآت، d للخصومات)
 * @returns {Object} - كائن يحتوي على whereClause و queryParams المحدثة
 */
function addDepartmentFilterToRewardsDeductions(whereClause, queryParams, req, tableAlias = '') {
  if (req.allowedDepartments === null) {
    // المستخدم admin - لا نطبق فلترة
    return { whereClause, queryParams };
  } else if (req.allowedDepartments && req.allowedDepartments.length > 0) {
    // تطبيق فلترة الإدارات المسموحة
    const departmentPlaceholders = req.allowedDepartments.map(() => '?').join(',');
    const departmentColumn = tableAlias ? `${tableAlias}.department` : 'department';
    whereClause += ` AND ${departmentColumn} IN (${departmentPlaceholders})`;
    queryParams.push(...req.allowedDepartments);
    return { whereClause, queryParams };
  } else {
    // لا يوجد إدارات مسموحة - لا يعرض أي نتائج
    whereClause += ` AND 1 = 0`;
    return { whereClause, queryParams };
  }
}

// متغير لتتبع ما إذا تم إنشاء الجداول أم لا
let rewardsTableCreated = false;
let deductionsTableCreated = false;

// إنشاء جدول المكافآت إذا لم يكن موجودًا (متطابق مع البنية الحالية)
const setupRewardsTable = async (poolConnection = null) => {
  if (rewardsTableCreated) return;

  const dbPool = poolConnection || pool;
  try {
    await dbPool.promise().query(`
      CREATE TABLE IF NOT EXISTS rewards (
        id int NOT NULL AUTO_INCREMENT PRIMARY KEY,
        employee_code varchar(50) NOT NULL,
        employee_name varchar(255) DEFAULT NULL,
        department varchar(255) DEFAULT NULL,
        amount decimal(10,2) NOT NULL,
        reason text NOT NULL,
        reward_date date NOT NULL,
        notes text,
        KEY idx_employee_code (employee_code),
        KEY idx_employee_name (employee_name),
        KEY idx_department (department),
        KEY idx_reward_date (reward_date)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    rewardsTableCreated = true;
    console.log('تم إنشاء جدول المكافآت بنجاح');
  } catch (error) {
    console.error('خطأ في إنشاء جدول المكافآت:', error);
    throw error;
  }
};

// إنشاء جدول الخصومات إذا لم يكن موجودًا (متطابق مع البنية الحالية)
const setupDeductionsTable = async (poolConnection = null) => {
  if (deductionsTableCreated) return;

  const dbPool = poolConnection || pool;
  try {
    await dbPool.promise().query(`
      CREATE TABLE IF NOT EXISTS deductions (
        id int NOT NULL AUTO_INCREMENT PRIMARY KEY,
        employee_code varchar(50) NOT NULL,
        employee_name varchar(255) DEFAULT NULL,
        department varchar(255) DEFAULT NULL,
        amount int NOT NULL,
        reason text NOT NULL,
        date date NOT NULL,
        notes text,
        KEY idx_employee_code (employee_code),
        KEY idx_employee_name (employee_name),
        KEY idx_department (department),
        KEY idx_date (date)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    deductionsTableCreated = true;
    console.log('تم إنشاء جدول الخصومات بنجاح');
  } catch (error) {
    console.error('خطأ في إنشاء جدول الخصومات:', error);
    throw error;
  }
};

// إعداد الجداول
router.get('/setup-tables', authenticateToken, checkPermission('can_add'), async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    await setupRewardsTable(pool);
    await setupDeductionsTable(pool);
    res.json({ message: 'تم إنشاء جداول المكافآت والخصومات بنجاح' });
  } catch (error) {
    console.error('خطأ في إنشاء الجداول:', error);
    res.status(500).json({ error: 'فشل في إنشاء الجداول' });
  }
});

// إعادة إنشاء الجداول (للتطوير)
router.get('/recreate-tables', authenticateToken, checkPermission('can_add'), async (req, res) => {
  try {
    const pool = req.app.locals.pool;

    console.log('بدء إعادة إنشاء الجداول...');

    // حذف الجداول الموجودة
    try {
      await pool.promise().query('DROP TABLE IF EXISTS rewards');
      console.log('تم حذف جدول المكافآت القديم');
    } catch (error) {
      console.log('لا يوجد جدول مكافآت للحذف');
    }

    try {
      await pool.promise().query('DROP TABLE IF EXISTS deductions');
      console.log('تم حذف جدول الخصومات القديم');
    } catch (error) {
      console.log('لا يوجد جدول خصومات للحذف');
    }

    // إعادة تعيين المتغيرات لإجبار إعادة الإنشاء
    rewardsTableCreated = false;
    deductionsTableCreated = false;

    // إنشاء الجداول الجديدة
    await setupRewardsTable(pool);
    await setupDeductionsTable(pool);

    console.log('تم إعادة إنشاء الجداول بنجاح');
    res.json({ message: 'تم إعادة إنشاء جداول المكافآت والخصومات بنجاح' });
  } catch (error) {
    console.error('خطأ في إعادة إنشاء الجداول:', error);
    console.error('Stack trace:', error.stack);
    res.status(500).json({ error: 'فشل في إعادة إنشاء الجداول', details: error.message });
  }
});

// اختبار route للتأكد من أن API يعمل
router.get('/test', (req, res) => {
  console.log('تم استقبال طلب اختبار');
  res.json({ message: 'API للمكافآت والخصومات يعمل بشكل صحيح', timestamp: new Date().toISOString() });
});

// اختبار routes للمكافآت والخصومات
router.get('/rewards/test', (req, res) => {
  console.log('تم استقبال طلب اختبار المكافآت');
  res.json({ message: 'route المكافآت يعمل', timestamp: new Date().toISOString() });
});

router.get('/deductions/test', (req, res) => {
  console.log('تم استقبال طلب اختبار الخصومات');
  res.json({ message: 'route الخصومات يعمل', timestamp: new Date().toISOString() });
});

// التحقق من بنية الجداول
router.get('/check-tables', authenticateToken, checkPermission('can_add'), async (req, res) => {
  try {
    const pool = req.app.locals.pool;

    // التحقق من جدول المكافآت
    let rewardsStructure = null;
    try {
      const [rewardsColumns] = await pool.promise().query('DESCRIBE rewards');
      rewardsStructure = rewardsColumns;
    } catch (error) {
      rewardsStructure = { error: 'الجدول غير موجود', details: error.message };
    }

    // التحقق من جدول الخصومات
    let deductionsStructure = null;
    try {
      const [deductionsColumns] = await pool.promise().query('DESCRIBE deductions');
      deductionsStructure = deductionsColumns;
    } catch (error) {
      deductionsStructure = { error: 'الجدول غير موجود', details: error.message };
    }

    res.json({
      rewards_table: rewardsStructure,
      deductions_table: deductionsStructure,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('خطأ في التحقق من الجداول:', error);
    res.status(500).json({ error: 'فشل في التحقق من الجداول', details: error.message });
  }
});

// إصلاح بنية جدول الخصومات
router.get('/fix-deductions-table', authenticateToken, checkPermission('can_add'), async (req, res) => {
  try {
    const pool = req.app.locals.pool;

    console.log('بدء إصلاح جدول الخصومات...');

    // التحقق من البنية الحالية
    const [columns] = await pool.promise().query('DESCRIBE deductions');
    console.log('البنية الحالية:', columns);

    // البحث عن عمود التاريخ
    const dateColumn = columns.find(col =>
      col.Field === 'date' ||
      col.Field === 'penalty_date' ||
      col.Field === 'deduction_date'
    );

    if (!dateColumn) {
      return res.status(400).json({ error: 'لم يتم العثور على عمود التاريخ' });
    }

    console.log('عمود التاريخ الموجود:', dateColumn.Field);

    // إذا كان العمود يسمى 'date'، غير اسمه إلى 'penalty_date'
    if (dateColumn.Field === 'date') {
      console.log('تغيير اسم العمود من date إلى penalty_date...');
      await pool.promise().query('ALTER TABLE deductions CHANGE COLUMN `date` `penalty_date` DATE NOT NULL');
      console.log('تم تغيير اسم العمود بنجاح');
    }

    // التحقق من البنية الجديدة
    const [newColumns] = await pool.promise().query('DESCRIBE deductions');

    res.json({
      message: 'تم إصلاح جدول الخصومات بنجاح',
      old_structure: columns,
      new_structure: newColumns,
      changed_column: dateColumn.Field === 'date' ? 'date -> penalty_date' : 'لا يوجد تغيير'
    });

  } catch (error) {
    console.error('خطأ في إصلاح جدول الخصومات:', error);
    console.error('Stack trace:', error.stack);
    res.status(500).json({ error: 'فشل في إصلاح جدول الخصومات', details: error.message });
  }
});

// DataTables server-side processing للمكافآت
router.get('/rewards/datatables', authenticateToken, addDepartmentFilter, checkPermission('view_rewards_list'), async (req, res) => {
  console.log('🔍 تم استقبال طلب DataTables للمكافآت');
  console.log('📥 Query parameters:', req.query);

  // استخراج معاملات البحث المتقدم
  const advancedSearchParams = {
    rewardId: req.query.rewardId || '',
    employeeCode: req.query.employeeCode || '',
    employeeName: req.query.employeeName || '',
    department: req.query.department || '',
    reason: req.query.reason || '',
    minAmount: req.query.minAmount || '',
    maxAmount: req.query.maxAmount || '',
    dateFrom: req.query.dateFrom || '',
    dateTo: req.query.dateTo || ''
  };

  console.log('🔍 معاملات البحث المستخرجة:', advancedSearchParams);

  try {
    const pool = req.app.locals.pool;
    await setupRewardsTable(pool);

    // معاملات DataTables
    const draw = parseInt(req.query.draw) || 1;
    const start = parseInt(req.query.start) || 0;
    const length = parseInt(req.query.length) || 10;
    const searchValue = req.query.search?.value || '';

    // حساب العدد الإجمالي مع فلترة الإدارات
    let totalQuery, totalParams;

    if (req.allowedDepartments === null) {
      totalQuery = 'SELECT COUNT(*) as total FROM rewards';
      totalParams = [];
    } else if (req.allowedDepartments && req.allowedDepartments.length > 0) {
      const departmentPlaceholders = req.allowedDepartments.map(() => '?').join(',');
      totalQuery = `
        SELECT COUNT(*) as total
        FROM rewards r
        INNER JOIN employees e ON r.employee_code = e.code
        WHERE e.department IN (${departmentPlaceholders}) AND e.department IS NOT NULL
      `;
      totalParams = req.allowedDepartments;
    } else {
      totalQuery = 'SELECT 0 as total';
      totalParams = [];
    }

    const [totalResult] = await pool.promise().query(totalQuery, totalParams);
    const totalRecords = totalResult[0].total;

    // بناء استعلام البحث مع فلترة الإدارات
    let searchQuery, searchParams;

    if (req.allowedDepartments === null) {
      // المستخدم admin - جلب جميع المكافآت
      searchQuery = `
        SELECT
          r.id,
          r.employee_code,
          r.employee_name,
          COALESCE(e.department, r.department) as department,
          r.amount,
          r.reason,
          DATE_FORMAT(r.reward_date, '%Y-%m-%d') as reward_date_formatted,
          r.notes
        FROM rewards r
        LEFT JOIN employees e ON r.employee_code = e.code
        WHERE 1=1
      `;
      searchParams = [];
    } else if (req.allowedDepartments && req.allowedDepartments.length > 0) {
      // المستخدم محدود - فلترة حسب الإدارات المسموحة
      const departmentPlaceholders = req.allowedDepartments.map(() => '?').join(',');
      searchQuery = `
        SELECT
          r.id,
          r.employee_code,
          r.employee_name,
          e.department,
          r.amount,
          r.reason,
          DATE_FORMAT(r.reward_date, '%Y-%m-%d') as reward_date_formatted,
          r.notes
        FROM rewards r
        INNER JOIN employees e ON r.employee_code = e.code
        WHERE e.department IN (${departmentPlaceholders}) AND e.department IS NOT NULL
      `;
      searchParams = [...req.allowedDepartments];
    } else {
      // لا يوجد إدارات مسموحة
      searchQuery = `
        SELECT
          id,
          employee_code,
          employee_name,
          department,
          amount,
          reason,
          DATE_FORMAT(reward_date, '%Y-%m-%d') as reward_date_formatted,
          notes
        FROM rewards
        WHERE 1 = 0
      `;
      searchParams = [];
    }

    // البحث العام
    if (searchValue) {
      searchQuery += ` AND (
        id LIKE ? OR
        employee_code LIKE ? OR
        employee_name LIKE ? OR
        department LIKE ? OR
        reason LIKE ? OR
        notes LIKE ?
      )`;
      const searchPattern = `%${searchValue}%`;
      searchParams.push(searchPattern, searchPattern, searchPattern, searchPattern, searchPattern, searchPattern);
    }

    // البحث المتقدم للمكافآت
    if (advancedSearchParams.rewardId) {
      searchQuery += " AND id = ?";
      searchParams.push(advancedSearchParams.rewardId);
      console.log('🔍 البحث بـ ID:', advancedSearchParams.rewardId);
    }

    if (advancedSearchParams.employeeCode) {
      searchQuery += " AND employee_code LIKE ?";
      searchParams.push(`%${advancedSearchParams.employeeCode}%`);
      console.log('🔍 البحث بكود الموظف:', advancedSearchParams.employeeCode);
    }

    if (advancedSearchParams.employeeName) {
      searchQuery += " AND employee_name LIKE ?";
      searchParams.push(`%${advancedSearchParams.employeeName}%`);
      console.log('🔍 البحث باسم الموظف:', advancedSearchParams.employeeName);
    }

    if (advancedSearchParams.department) {
      searchQuery += " AND department LIKE ?";
      searchParams.push(`%${advancedSearchParams.department}%`);
      console.log('🔍 البحث بالقسم:', advancedSearchParams.department);
    }

    if (advancedSearchParams.reason) {
      searchQuery += " AND reason LIKE ?";
      searchParams.push(`%${advancedSearchParams.reason}%`);
      console.log('🔍 البحث بالسبب:', advancedSearchParams.reason);
    }

    if (advancedSearchParams.minAmount) {
      searchQuery += " AND amount >= ?";
      searchParams.push(parseFloat(advancedSearchParams.minAmount));
      console.log('🔍 البحث بالحد الأدنى للمبلغ:', advancedSearchParams.minAmount);
    }

    if (advancedSearchParams.maxAmount) {
      searchQuery += " AND amount <= ?";
      searchParams.push(parseFloat(advancedSearchParams.maxAmount));
      console.log('🔍 البحث بالحد الأقصى للمبلغ:', advancedSearchParams.maxAmount);
    }

    if (advancedSearchParams.dateFrom) {
      searchQuery += " AND reward_date >= ?";
      searchParams.push(advancedSearchParams.dateFrom);
      console.log('🔍 البحث من تاريخ:', advancedSearchParams.dateFrom);
    }

    if (advancedSearchParams.dateTo) {
      searchQuery += " AND reward_date <= ?";
      searchParams.push(advancedSearchParams.dateTo);
      console.log('🔍 البحث إلى تاريخ:', advancedSearchParams.dateTo);
    }

    console.log('📝 الاستعلام النهائي:', searchQuery);
    console.log('📋 المعاملات النهائية:', searchParams);

    // حساب العدد المفلتر
    let countQuery = searchQuery.replace(/SELECT[\s\S]*?FROM/, 'SELECT COUNT(*) as filtered FROM');
    const [countResult] = await pool.promise().query(countQuery, searchParams);
    const filteredRecords = countResult[0].filtered;

    // إضافة الترتيب والحد
    searchQuery += ` ORDER BY id DESC LIMIT ? OFFSET ?`;
    searchParams.push(length, start);

    const [rows] = await pool.promise().query(searchQuery, searchParams);

    // تنسيق البيانات لـ DataTables
    const formattedData = rows.map(row => {
      // إنشاء أزرار الإجراءات (سيتم إخفاؤها في الواجهة الأمامية حسب الصلاحيات)
      const actionsHtml = `
        <button class="edit-reward-btn btn btn-sm btn-warning me-1" data-reward-id="${row.id}">تعديل</button>
        <button class="delete-reward-btn btn btn-sm btn-danger" data-reward-id="${row.id}">حذف</button>
      `;

      return [
        row.id,
        row.employee_code,
        row.employee_name,
        row.department,
        parseFloat(row.amount) % 1 === 0 ? parseInt(row.amount) : parseFloat(row.amount).toFixed(2),
        row.reason,
        row.reward_date_formatted,
        row.notes || '-',
        actionsHtml
      ];
    });

    res.json({
      draw: draw,
      recordsTotal: totalRecords,
      recordsFiltered: filteredRecords,
      data: formattedData
    });

  } catch (error) {
    console.error('خطأ في DataTables للمكافآت:', error);
    console.error('Stack trace:', error.stack);
    res.status(500).json({
      error: 'فشل في جلب البيانات',
      message: error.message,
      draw: parseInt(req.query.draw) || 1,
      recordsTotal: 0,
      recordsFiltered: 0,
      data: []
    });
  }
});

// DataTables server-side processing للخصومات
router.get('/deductions/datatables', authenticateToken, addDepartmentFilter, checkPermission('view_deductions_list'), async (req, res) => {
  console.log('🔍 تم استقبال طلب DataTables للخصومات');
  console.log('📥 Query parameters:', req.query);

  // استخراج معاملات البحث المتقدم
  const advancedSearchParams = {
    deductionId: req.query.deductionId || '',
    employeeCode: req.query.employeeCode || '',
    employeeName: req.query.employeeName || '',
    department: req.query.department || '',
    reason: req.query.reason || '',
    minAmount: req.query.minAmount || '',
    maxAmount: req.query.maxAmount || '',
    dateFrom: req.query.dateFrom || '',
    dateTo: req.query.dateTo || ''
  };

  console.log('🔍 معاملات البحث المستخرجة:', advancedSearchParams);

  try {
    const pool = req.app.locals.pool;
    console.log('إعداد جدول الخصومات...');
    await setupDeductionsTable(pool);
    console.log('تم إعداد جدول الخصومات');

    // استخدام اسم العمود الصحيح حسب بنية قاعدة البيانات
    const dateColumnName = 'date'; // العمود الفعلي في قاعدة البيانات
    console.log(`استخدام عمود التاريخ: ${dateColumnName}`);

    // معاملات DataTables
    const draw = parseInt(req.query.draw) || 1;
    const start = parseInt(req.query.start) || 0;
    const length = parseInt(req.query.length) || 10;
    const searchValue = req.query.search?.value || '';

    // حساب العدد الإجمالي مع فلترة الإدارات
    console.log('حساب العدد الإجمالي...');
    let totalQuery, totalParams;

    if (req.allowedDepartments === null) {
      totalQuery = 'SELECT COUNT(*) as total FROM deductions';
      totalParams = [];
    } else if (req.allowedDepartments && req.allowedDepartments.length > 0) {
      const departmentPlaceholders = req.allowedDepartments.map(() => '?').join(',');
      totalQuery = `
        SELECT COUNT(*) as total
        FROM deductions d
        INNER JOIN employees e ON d.employee_code = e.code
        WHERE e.department IN (${departmentPlaceholders}) AND e.department IS NOT NULL
      `;
      totalParams = req.allowedDepartments;
    } else {
      totalQuery = 'SELECT 0 as total';
      totalParams = [];
    }

    const [totalResult] = await pool.promise().query(totalQuery, totalParams);
    const totalRecords = totalResult[0].total;
    console.log(`العدد الإجمالي: ${totalRecords}`);

    // بناء استعلام البحث مع فلترة الإدارات
    let searchQuery, searchParams;

    if (req.allowedDepartments === null) {
      // المستخدم admin - جلب جميع الخصومات
      searchQuery = `
        SELECT
          d.id,
          d.employee_code,
          d.employee_name,
          COALESCE(e.department, d.department) as department,
          d.amount,
          d.reason,
          DATE_FORMAT(d.date, '%Y-%m-%d') as date_formatted,
          d.notes
        FROM deductions d
        LEFT JOIN employees e ON d.employee_code = e.code
        WHERE 1=1
      `;
      searchParams = [];
    } else if (req.allowedDepartments && req.allowedDepartments.length > 0) {
      // المستخدم محدود - فلترة حسب الإدارات المسموحة
      const departmentPlaceholders = req.allowedDepartments.map(() => '?').join(',');
      searchQuery = `
        SELECT
          d.id,
          d.employee_code,
          d.employee_name,
          e.department,
          d.amount,
          d.reason,
          DATE_FORMAT(d.date, '%Y-%m-%d') as date_formatted,
          d.notes
        FROM deductions d
        INNER JOIN employees e ON d.employee_code = e.code
        WHERE e.department IN (${departmentPlaceholders}) AND e.department IS NOT NULL
      `;
      searchParams = [...req.allowedDepartments];
    } else {
      // لا يوجد إدارات مسموحة
      searchQuery = `
        SELECT
          id,
          employee_code,
          employee_name,
          department,
          amount,
          reason,
          DATE_FORMAT(date, '%Y-%m-%d') as date_formatted,
          notes
        FROM deductions
        WHERE 1 = 0
      `;
      searchParams = [];
    }

    // البحث العام
    if (searchValue) {
      searchQuery += ` AND (
        id LIKE ? OR
        employee_code LIKE ? OR
        employee_name LIKE ? OR
        department LIKE ? OR
        reason LIKE ? OR
        notes LIKE ?
      )`;
      const searchPattern = `%${searchValue}%`;
      searchParams.push(searchPattern, searchPattern, searchPattern, searchPattern, searchPattern, searchPattern);
    }

    // البحث المتقدم للخصومات
    if (advancedSearchParams.deductionId) {
      searchQuery += " AND id = ?";
      searchParams.push(advancedSearchParams.deductionId);
      console.log('🔍 البحث بـ ID:', advancedSearchParams.deductionId);
    }

    if (advancedSearchParams.employeeCode) {
      searchQuery += " AND employee_code LIKE ?";
      searchParams.push(`%${advancedSearchParams.employeeCode}%`);
      console.log('🔍 البحث بكود الموظف:', advancedSearchParams.employeeCode);
    }

    if (advancedSearchParams.employeeName) {
      searchQuery += " AND employee_name LIKE ?";
      searchParams.push(`%${advancedSearchParams.employeeName}%`);
      console.log('🔍 البحث باسم الموظف:', advancedSearchParams.employeeName);
    }

    if (advancedSearchParams.department) {
      searchQuery += " AND department LIKE ?";
      searchParams.push(`%${advancedSearchParams.department}%`);
      console.log('🔍 البحث بالقسم:', advancedSearchParams.department);
    }

    if (advancedSearchParams.reason) {
      searchQuery += " AND reason LIKE ?";
      searchParams.push(`%${advancedSearchParams.reason}%`);
      console.log('🔍 البحث بالسبب:', advancedSearchParams.reason);
    }

    if (advancedSearchParams.minAmount) {
      searchQuery += " AND amount >= ?";
      searchParams.push(parseInt(advancedSearchParams.minAmount));
      console.log('🔍 البحث بالحد الأدنى للمبلغ:', advancedSearchParams.minAmount);
    }

    if (advancedSearchParams.maxAmount) {
      searchQuery += " AND amount <= ?";
      searchParams.push(parseInt(advancedSearchParams.maxAmount));
      console.log('🔍 البحث بالحد الأقصى للمبلغ:', advancedSearchParams.maxAmount);
    }

    if (advancedSearchParams.dateFrom) {
      searchQuery += " AND date >= ?";
      searchParams.push(advancedSearchParams.dateFrom);
      console.log('🔍 البحث من تاريخ:', advancedSearchParams.dateFrom);
    }

    if (advancedSearchParams.dateTo) {
      searchQuery += " AND date <= ?";
      searchParams.push(advancedSearchParams.dateTo);
      console.log('🔍 البحث إلى تاريخ:', advancedSearchParams.dateTo);
    }

    console.log('📝 الاستعلام النهائي:', searchQuery);
    console.log('📋 المعاملات النهائية:', searchParams);

    // حساب العدد المفلتر
    let countQuery = searchQuery.replace(/SELECT[\s\S]*?FROM/, 'SELECT COUNT(*) as filtered FROM');
    const [countResult] = await pool.promise().query(countQuery, searchParams);
    const filteredRecords = countResult[0].filtered;

    // إضافة الترتيب والحد
    searchQuery += ` ORDER BY id DESC LIMIT ? OFFSET ?`;
    searchParams.push(length, start);

    const [rows] = await pool.promise().query(searchQuery, searchParams);

    // تنسيق البيانات لـ DataTables
    const formattedData = rows.map(row => {
      // إنشاء أزرار الإجراءات (سيتم إخفاؤها في الواجهة الأمامية حسب الصلاحيات)
      const actionsHtml = `
        <button class="edit-deduction-btn btn btn-sm btn-warning me-1" data-deduction-id="${row.id}">تعديل</button>
        <button class="delete-deduction-btn btn btn-sm btn-danger" data-deduction-id="${row.id}">حذف</button>
      `;

      return [
        row.id,
        row.employee_code,
        row.employee_name || '-',
        row.department || '-',
        parseInt(row.amount), // amount هو int في قاعدة البيانات
        row.reason,
        row.date_formatted,
        row.notes || '-',
        actionsHtml
      ];
    });

    res.json({
      draw: draw,
      recordsTotal: totalRecords,
      recordsFiltered: filteredRecords,
      data: formattedData
    });

  } catch (error) {
    console.error('خطأ في DataTables للخصومات:', error);
    console.error('Stack trace:', error.stack);
    res.status(500).json({
      error: 'فشل في جلب البيانات',
      message: error.message,
      draw: parseInt(req.query.draw) || 1,
      recordsTotal: 0,
      recordsFiltered: 0,
      data: []
    });
  }
});

// الحصول على مكافأة واحدة
router.get('/rewards/:id', authenticateToken, checkPermission('view_rewards_list'), async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const rewardId = req.params.id;

    const [rows] = await pool.promise().query(
      'SELECT * FROM rewards WHERE id = ?',
      [rewardId]
    );

    if (rows.length === 0) {
      return res.status(404).json({ error: 'المكافأة غير موجودة' });
    }

    res.json(rows[0]);
  } catch (error) {
    console.error('خطأ في جلب المكافأة:', error);
    res.status(500).json({ error: 'فشل في جلب المكافأة' });
  }
});

// الحصول على خصم واحد
router.get('/deductions/:id', authenticateToken, checkPermission('view_deductions_list'), async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const deductionId = req.params.id;

    const [rows] = await pool.promise().query(`
      SELECT
        id,
        employee_code,
        employee_name,
        department,
        amount,
        reason,
        DATE_FORMAT(date, '%Y-%m-%d') as date,
        notes
      FROM deductions
      WHERE id = ?
    `, [deductionId]);

    if (rows.length === 0) {
      return res.status(404).json({ error: 'الخصم غير موجود' });
    }

    res.json(rows[0]);
  } catch (error) {
    console.error('خطأ في جلب الخصم:', error);
    res.status(500).json({ error: 'فشل في جلب الخصم' });
  }
});

// جلب جميع المكافآت
router.get('/rewards', authenticateToken, addDepartmentFilter, checkPermission('view_rewards_list'), async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    console.log('محاولة إعداد جدول المكافآت...');
    await setupRewardsTable(pool);
    console.log('تم إعداد جدول المكافآت بنجاح');

    console.log('محاولة جلب بيانات المكافآت...');

    let whereClause = 'WHERE 1=1';
    let queryParams = [];

    // إضافة فلترة الإدارات
    const departmentFilter = addDepartmentFilterToRewardsDeductions(whereClause, queryParams, req);

    // استخدام JOIN مع جدول employees للحصول على الإدارة الحالية
    let query, params;

    if (req.allowedDepartments === null) {
      // المستخدم admin - جلب جميع المكافآت
      query = `
        SELECT
          r.id,
          r.employee_code,
          r.employee_name,
          COALESCE(e.department, r.department) as department,
          r.amount,
          r.reason,
          DATE_FORMAT(r.reward_date, '%Y-%m-%d') as reward_date,
          r.notes,
          r.created_at,
          r.updated_at
        FROM rewards r
        LEFT JOIN employees e ON r.employee_code = e.code
        ORDER BY r.created_at DESC
      `;
      params = [];
    } else {
      // المستخدم محدود - فلترة حسب الإدارة الحالية للموظف
      const departmentPlaceholders = req.allowedDepartments.map(() => '?').join(',');
      query = `
        SELECT
          r.id,
          r.employee_code,
          r.employee_name,
          COALESCE(e.department, r.department) as department,
          r.amount,
          r.reason,
          DATE_FORMAT(r.reward_date, '%Y-%m-%d') as reward_date,
          r.notes,
          r.created_at,
          r.updated_at
        FROM rewards r
        INNER JOIN employees e ON r.employee_code = e.code
        WHERE e.department IN (${departmentPlaceholders}) AND e.department IS NOT NULL
        ORDER BY r.created_at DESC
      `;
      params = req.allowedDepartments;
    }

    const [rows] = await pool.promise().query(query, params);

    console.log(`تم جلب ${rows.length} سجل من المكافآت`);
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب المكافآت:', error);
    console.error('Stack trace:', error.stack);
    res.status(500).json({ error: 'فشل في جلب المكافآت', details: error.message });
  }
});

// جلب جميع الخصومات
router.get('/deductions', authenticateToken, addDepartmentFilter, checkPermission('view_deductions_list'), async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    console.log('محاولة إعداد جدول الخصومات...');
    await setupDeductionsTable(pool);
    console.log('تم إعداد جدول الخصومات بنجاح');

    console.log('محاولة جلب بيانات الخصومات...');

    let whereClause = 'WHERE 1=1';
    let queryParams = [];

    // إضافة فلترة الإدارات
    const departmentFilter = addDepartmentFilterToRewardsDeductions(whereClause, queryParams, req);

    // استخدام JOIN مع جدول employees للحصول على الإدارة الحالية
    let query, params;

    if (req.allowedDepartments === null) {
      // المستخدم admin - جلب جميع الخصومات
      query = `
        SELECT
          d.id,
          d.employee_code,
          d.employee_name,
          COALESCE(e.department, d.department) as department,
          d.amount,
          d.reason,
          DATE_FORMAT(d.date, '%Y-%m-%d') as date,
          d.notes
        FROM deductions d
        LEFT JOIN employees e ON d.employee_code = e.code
        ORDER BY d.id DESC
      `;
      params = [];
    } else {
      // المستخدم محدود - فلترة حسب الإدارة الحالية للموظف
      const departmentPlaceholders = req.allowedDepartments.map(() => '?').join(',');
      query = `
        SELECT
          d.id,
          d.employee_code,
          d.employee_name,
          COALESCE(e.department, d.department) as department,
          d.amount,
          d.reason,
          DATE_FORMAT(d.date, '%Y-%m-%d') as date,
          d.notes
        FROM deductions d
        INNER JOIN employees e ON d.employee_code = e.code
        WHERE e.department IN (${departmentPlaceholders}) AND e.department IS NOT NULL
        ORDER BY d.id DESC
      `;
      params = req.allowedDepartments;
    }

    const [rows] = await pool.promise().query(query, params);

    console.log(`تم جلب ${rows.length} سجل من الخصومات`);
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب الخصومات:', error);
    console.error('Stack trace:', error.stack);
    res.status(500).json({ error: 'فشل في جلب الخصومات', details: error.message });
  }
});

// إضافة بيانات اختبار (للتطوير)
router.post('/add-test-data', authenticateToken, checkPermission('can_add'), async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    await setupRewardsTable(pool);
    await setupDeductionsTable(pool);

    // إضافة مكافآت اختبار
    const rewardsTestData = [
      {
        employee_code: 'EMP001',
        employee_name: 'أحمد محمد',
        department: 'المبيعات',
        amount: 500.00,
        reason: 'تحقيق أهداف المبيعات',
        reward_date: '2024-01-15',
        notes: 'أداء ممتاز'
      },
      {
        employee_code: 'EMP002',
        employee_name: 'فاطمة علي',
        department: 'التسويق',
        amount: 750.00,
        reason: 'حملة تسويقية ناجحة',
        reward_date: '2024-01-20',
        notes: 'إبداع في التسويق'
      }
    ];

    // إضافة خصومات اختبار
    const deductionsTestData = [
      {
        employee_code: 'EMP003',
        employee_name: 'محمد أحمد',
        department: 'المحاسبة',
        amount: 200, // int كما في قاعدة البيانات
        reason: 'تأخير في تسليم التقارير',
        date: '2024-01-10', // استخدام 'date' بدلاً من 'penalty_date'
        notes: 'تحذير أول'
      },
      {
        employee_code: 'EMP004',
        employee_name: 'سارة محمود',
        department: 'الموارد البشرية',
        amount: 150, // int كما في قاعدة البيانات
        reason: 'غياب بدون إذن',
        date: '2024-01-12', // استخدام 'date' بدلاً من 'penalty_date'
        notes: 'يوم واحد'
      }
    ];

    // إدراج المكافآت
    for (const reward of rewardsTestData) {
      await pool.promise().query(`
        INSERT INTO rewards (employee_code, employee_name, department, amount, reason, reward_date, notes)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [reward.employee_code, reward.employee_name, reward.department, reward.amount, reward.reason, reward.reward_date, reward.notes]);
    }

    // إدراج الخصومات
    for (const deduction of deductionsTestData) {
      await pool.promise().query(`
        INSERT INTO deductions (employee_code, employee_name, department, amount, reason, date, notes)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [deduction.employee_code, deduction.employee_name, deduction.department, deduction.amount, deduction.reason, deduction.date, deduction.notes]);
    }

    res.json({
      message: 'تم إضافة بيانات الاختبار بنجاح',
      rewards_added: rewardsTestData.length,
      deductions_added: deductionsTestData.length
    });

  } catch (error) {
    console.error('خطأ في إضافة بيانات الاختبار:', error);
    res.status(500).json({ error: 'فشل في إضافة بيانات الاختبار', details: error.message });
  }
});

// إضافة مكافأة جديدة
router.post('/rewards', authenticateToken, checkPermission('can_add'), async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    await setupRewardsTable(pool);

    const { employee_code, employee_name, department, amount, reason, reward_date, notes } = req.body;

    // التحقق من البيانات المطلوبة
    if (!employee_code || !employee_name || !department || !amount || !reason || !reward_date) {
      return res.status(400).json({ error: 'جميع الحقول مطلوبة' });
    }

    const [result] = await pool.promise().query(`
      INSERT INTO rewards (employee_code, employee_name, department, amount, reason, reward_date, notes)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [employee_code, employee_name, department, parseFloat(amount), reason, reward_date, notes || null]);

    // تسجيل النشاط
    try {
      await logAction({
        user_id: req.user.id,
        username: req.user.username,
        action_type: 'إضافة',
        module: 'rewards',
        record_id: result.insertId,
        message: `تم إضافة مكافأة للموظف ${employee_name} (${employee_code}) بمبلغ ${amount} جنيه`
      });
      console.log('تم تسجيل النشاط بنجاح');
    } catch (logError) {
      console.error('خطأ في تسجيل النشاط:', logError);
    }

    res.json({
      message: 'تم إضافة المكافأة بنجاح',
      id: result.insertId
    });

  } catch (error) {
    console.error('خطأ في إضافة المكافأة:', error);
    res.status(500).json({ error: 'فشل في إضافة المكافأة', details: error.message });
  }
});

// إضافة خصم جديد
router.post('/deductions', authenticateToken, checkPermission('can_add'), async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    await setupDeductionsTable(pool);

    const { employee_code, employee_name, department, amount, reason, date, notes } = req.body;

    // التحقق من البيانات المطلوبة
    if (!employee_code || !employee_name || !department || !amount || !reason || !date) {
      return res.status(400).json({ error: 'جميع الحقول مطلوبة' });
    }

    const [result] = await pool.promise().query(`
      INSERT INTO deductions (employee_code, employee_name, department, amount, reason, date, notes)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [employee_code, employee_name, department, parseInt(amount), reason, date, notes || null]);

    // تسجيل النشاط
    try {
      await logAction({
        user_id: req.user.id,
        username: req.user.username,
        action_type: 'إضافة',
        module: 'deductions',
        record_id: result.insertId,
        message: `تم إضافة خصم للموظف ${employee_name} (${employee_code}) بمبلغ ${amount} جنيه`
      });
      console.log('تم تسجيل النشاط بنجاح');
    } catch (logError) {
      console.error('خطأ في تسجيل النشاط:', logError);
    }

    res.json({
      message: 'تم إضافة الخصم بنجاح',
      id: result.insertId
    });

  } catch (error) {
    console.error('خطأ في إضافة الخصم:', error);
    res.status(500).json({ error: 'فشل في إضافة الخصم', details: error.message });
  }
});

// تعديل مكافأة
router.put('/rewards/:id', authenticateToken, checkPermission('can_edit'), async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const rewardId = req.params.id;
    const { employee_code, employee_name, department, amount, reason, reward_date, notes } = req.body;

    // التحقق من البيانات المطلوبة
    if (!employee_code || !employee_name || !department || !amount || !reason || !reward_date) {
      return res.status(400).json({ error: 'جميع الحقول مطلوبة' });
    }

    const [result] = await pool.promise().query(`
      UPDATE rewards
      SET employee_code = ?, employee_name = ?, department = ?, amount = ?, reason = ?, reward_date = ?, notes = ?
      WHERE id = ?
    `, [employee_code, employee_name, department, parseFloat(amount), reason, reward_date, notes || null, rewardId]);

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'المكافأة غير موجودة' });
    }

    // تسجيل النشاط
    await logAction({
      user_id: req.user.id,
      username: req.user.username,
      action_type: 'تعديل',
      module: 'rewards',
      record_id: rewardId,
      message: `تم تعديل مكافأة للموظف ${employee_name} (${employee_code}) - المبلغ: ${amount} جنيه`
    });

    res.json({ message: 'تم تعديل المكافأة بنجاح' });

  } catch (error) {
    console.error('خطأ في تعديل المكافأة:', error);
    res.status(500).json({ error: 'فشل في تعديل المكافأة', details: error.message });
  }
});

// تعديل خصم
router.put('/deductions/:id', authenticateToken, checkPermission('can_edit'), async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const deductionId = req.params.id;
    const { employee_code, employee_name, department, amount, reason, date, notes } = req.body;

    // التحقق من البيانات المطلوبة
    if (!employee_code || !employee_name || !department || !amount || !reason || !date) {
      return res.status(400).json({ error: 'جميع الحقول مطلوبة' });
    }

    const [result] = await pool.promise().query(`
      UPDATE deductions
      SET employee_code = ?, employee_name = ?, department = ?, amount = ?, reason = ?, date = ?, notes = ?
      WHERE id = ?
    `, [employee_code, employee_name, department, parseInt(amount), reason, date, notes || null, deductionId]);

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'الخصم غير موجود' });
    }

    // تسجيل النشاط
    await logAction({
      user_id: req.user.id,
      username: req.user.username,
      action_type: 'تعديل',
      module: 'deductions',
      record_id: deductionId,
      message: `تم تعديل خصم للموظف ${employee_name} (${employee_code}) - المبلغ: ${amount} جنيه`
    });

    res.json({ message: 'تم تعديل الخصم بنجاح' });

  } catch (error) {
    console.error('خطأ في تعديل الخصم:', error);
    res.status(500).json({ error: 'فشل في تعديل الخصم', details: error.message });
  }
});

// حذف مكافأة
router.delete('/rewards/:id', authenticateToken, checkPermission('can_delete'), async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const rewardId = req.params.id;

    // الحصول على بيانات المكافأة قبل الحذف للتسجيل
    const [rewardData] = await pool.promise().query('SELECT employee_name, employee_code, amount FROM rewards WHERE id = ?', [rewardId]);

    const [result] = await pool.promise().query('DELETE FROM rewards WHERE id = ?', [rewardId]);

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'المكافأة غير موجودة' });
    }

    // تسجيل النشاط
    if (rewardData.length > 0) {
      const reward = rewardData[0];
      await logAction({
        user_id: req.user.id,
        username: req.user.username,
        action_type: 'حذف',
        module: 'rewards',
        record_id: rewardId,
        message: `تم حذف مكافأة للموظف ${reward.employee_name} (${reward.employee_code}) بمبلغ ${reward.amount} جنيه`
      });
    }

    res.json({ message: 'تم حذف المكافأة بنجاح' });

  } catch (error) {
    console.error('خطأ في حذف المكافأة:', error);
    res.status(500).json({ error: 'فشل في حذف المكافأة', details: error.message });
  }
});

// حذف خصم
router.delete('/deductions/:id', authenticateToken, checkPermission('can_delete'), async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const deductionId = req.params.id;

    // الحصول على بيانات الخصم قبل الحذف للتسجيل
    const [deductionData] = await pool.promise().query('SELECT employee_name, employee_code, amount FROM deductions WHERE id = ?', [deductionId]);

    const [result] = await pool.promise().query('DELETE FROM deductions WHERE id = ?', [deductionId]);

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'الخصم غير موجود' });
    }

    // تسجيل النشاط
    if (deductionData.length > 0) {
      const deduction = deductionData[0];
      await logAction({
        user_id: req.user.id,
        username: req.user.username,
        action_type: 'حذف',
        module: 'deductions',
        record_id: deductionId,
        message: `تم حذف خصم للموظف ${deduction.employee_name} (${deduction.employee_code}) بمبلغ ${deduction.amount} جنيه`
      });
    }

    res.json({ message: 'تم حذف الخصم بنجاح' });

  } catch (error) {
    console.error('خطأ في حذف الخصم:', error);
    res.status(500).json({ error: 'فشل في حذف الخصم', details: error.message });
  }
});

module.exports = router;
