const express = require('express');
const router = express.Router();
const { logAction, createEditMessage } = require('../activityLogger');
const { authenticateToken } = require('../middleware/auth');
const { addDepartmentFilter } = require('../middleware/departmentFilter');
const { cleanUpdateData, cleanInsertData, prepareUpdateQuery, prepareInsertQuery } = require('../utils/dataCleanup');

/**
 * دالة لإضافة فلترة الإدارات لاستعلامات الساعات الإضافية
 * @param {string} whereClause - شرط WHERE الحالي
 * @param {Array} queryParams - معاملات الاستعلام الحالية
 * @param {Object} req - كائن الطلب
 * @returns {Object} - كائن يحتوي على whereClause و queryParams المحدثة
 */
function addDepartmentFilterToExtraHours(whereClause, queryParams, req) {
  if (req.allowedDepartments === null) {
    // المستخدم admin - لا نطبق فلترة
    return { whereClause, queryParams };
  } else if (req.allowedDepartments && req.allowedDepartments.length > 0) {
    // تطبيق فلترة الإدارات المسموحة
    const departmentPlaceholders = req.allowedDepartments.map(() => '?').join(',');
    whereClause += ` AND department IN (${departmentPlaceholders})`;
    queryParams.push(...req.allowedDepartments);
    return { whereClause, queryParams };
  } else {
    // لا يوجد إدارات مسموحة - لا يعرض أي نتائج
    whereClause += ` AND 1 = 0`;
    return { whereClause, queryParams };
  }
}

// دالة لإنشاء جدول الساعات الإضافية
const createExtraHoursTable = async (pool) => {
  const sql = `CREATE TABLE IF NOT EXISTS extra_hours (
    id int NOT NULL AUTO_INCREMENT,
    employee_code varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'كود الموظف',
    employee_name varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'اسم الموظف',
    department varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'الإدارة',
    extra_hours decimal(5,2) NOT NULL COMMENT 'عدد الساعات الإضافية',
    extra_date date NOT NULL COMMENT 'تاريخ الإضافي',
    notes text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'ملاحظات',
    created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ التحديث',
    PRIMARY KEY (id),
    KEY idx_employee_code (employee_code),
    KEY idx_department (department),
    KEY idx_extra_date (extra_date),
    KEY idx_extra_hours (extra_hours)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الساعات الإضافية'`;
  
  await pool.promise().query(sql);
};

// إنشاء جدول الساعات الإضافية
router.get('/create-table', async (req, res) => {
  try {
    await createExtraHoursTable(req.app.locals.pool);
    res.json({ message: 'تم التحقق من جدول الساعات الإضافية' });
  } catch (error) {
    console.error('خطأ في إنشاء جدول الساعات الإضافية:', error);
    res.status(500).json({ error: 'فشل في إنشاء جدول الساعات الإضافية' });
  }
});

// الحصول على جميع الساعات الإضافية
router.get('/', authenticateToken, addDepartmentFilter, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    await createExtraHoursTable(pool);

    let whereClause = 'WHERE 1=1';
    let queryParams = [];

    // إضافة فلترة الإدارات
    const departmentFilter = addDepartmentFilterToExtraHours(whereClause, queryParams, req);

    const [rows] = await pool.promise().query(`
      SELECT
        eh.*,
        DATE_FORMAT(eh.extra_date, '%Y-%m-%d') as extra_date_formatted,
        CASE
          WHEN e.full_name IS NOT NULL THEN e.full_name
          ELSE eh.employee_name
        END as current_employee_name,
        CASE
          WHEN e.department IS NOT NULL THEN e.department
          ELSE eh.department
        END as current_department
      FROM extra_hours eh
      LEFT JOIN employees e ON eh.employee_code = e.code
      ${departmentFilter.whereClause.replace('department', 'eh.department')}
      ORDER BY eh.created_at DESC, eh.id DESC
    `, departmentFilter.queryParams);
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب الساعات الإضافية:', error);
    res.status(500).json({ error: 'فشل في جلب بيانات الساعات الإضافية' });
  }
});

// إضافة ساعات إضافية جديدة
router.post('/', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    await createExtraHoursTable(pool);
    
    const { 
      employee_code, 
      extra_hours, 
      extra_date, 
      notes 
    } = req.body;

    // التحقق من البيانات المطلوبة
    if (!employee_code || !extra_hours || !extra_date) {
      return res.status(400).json({ error: 'جميع الحقول المطلوبة يجب تعبئتها' });
    }

    // التحقق من صحة عدد الساعات
    if (parseFloat(extra_hours) <= 0) {
      return res.status(400).json({ error: 'عدد الساعات الإضافية يجب أن يكون أكبر من صفر' });
    }

    // الحصول على بيانات الموظف
    const [employeeResult] = await pool.promise().query(
      'SELECT full_name, department, status FROM employees WHERE code = ?',
      [employee_code]
    );

    if (employeeResult.length === 0) {
      return res.status(400).json({ error: 'الموظف غير موجود' });
    }

    // التحقق من حالة الموظف
    if (employeeResult[0].status === 'مستقيل') {
      return res.status(400).json({ error: 'لا يمكن إضافة ساعات إضافية لموظف مستقيل' });
    }

    // فحص عدم تكرار نفس الموظف في نفس التاريخ
    const [existingRecord] = await pool.promise().query(
      'SELECT id FROM extra_hours WHERE employee_code = ? AND extra_date = ?',
      [employee_code, extra_date]
    );

    if (existingRecord.length > 0) {
      return res.status(409).json({
        error: 'تم تسجيل عمل إضافي لهذا الموظف في نفس التاريخ مسبقاً',
        duplicate: true,
        existing_date: extra_date
      });
    }

    const employeeName = employeeResult[0].full_name;
    const department = employeeResult[0].department;

    const sql = `
      INSERT INTO extra_hours (
        employee_code, employee_name, department, extra_hours, 
        extra_date, notes
      ) VALUES (?, ?, ?, ?, ?, ?)
    `;
    
    const values = [
      employee_code,
      employeeName,
      department,
      parseFloat(extra_hours),
      extra_date,
      notes || null
    ];
    
    const [result] = await pool.promise().query(sql, values);

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'add',
      module: 'extra_hours',
      record_id: result.insertId.toString(),
      message: `تم إضافة ساعات إضافية للموظف: ${employeeName} (كود: ${employee_code}) - عدد الساعات: ${extra_hours} ساعة - التاريخ: ${extra_date}`
    });

    res.status(201).json({
      message: 'تم إضافة الساعات الإضافية بنجاح',
      id: result.insertId
    });
  } catch (error) {
    console.error('خطأ في إضافة الساعات الإضافية:', error);
    res.status(500).json({ error: 'فشل في إضافة الساعات الإضافية' });
  }
});

// تحديث ساعات إضافية
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const extraHourId = req.params.id;

    const {
      employee_code,
      extra_hours,
      extra_date,
      notes
    } = req.body;

    // التحقق من البيانات المطلوبة
    if (!employee_code || !extra_hours || !extra_date) {
      return res.status(400).json({ error: 'جميع الحقول المطلوبة يجب تعبئتها' });
    }

    // التحقق من صحة عدد الساعات
    if (parseFloat(extra_hours) <= 0) {
      return res.status(400).json({ error: 'عدد الساعات الإضافية يجب أن يكون أكبر من صفر' });
    }

    // الحصول على البيانات القديمة قبل التعديل
    const [oldDataResult] = await pool.promise().query(
      'SELECT * FROM extra_hours WHERE id = ?',
      [extraHourId]
    );

    if (oldDataResult.length === 0) {
      return res.status(404).json({ error: 'السجل غير موجود' });
    }

    const oldData = oldDataResult[0];

    // الحصول على بيانات الموظف
    const [employeeResult] = await pool.promise().query(
      'SELECT full_name, department FROM employees WHERE code = ?',
      [employee_code]
    );

    if (employeeResult.length === 0) {
      return res.status(400).json({ error: 'الموظف غير موجود' });
    }

    const employeeName = employeeResult[0].full_name;
    const department = employeeResult[0].department;

    // البيانات الجديدة
    const newData = {
      employee_code,
      employee_name: employeeName,
      department,
      extra_hours: parseFloat(extra_hours),
      extra_date,
      notes: notes || null
    };

    // تنظيف البيانات ومعالجة حقول التاريخ
    const cleanedData = cleanUpdateData(newData);

    // تحضير استعلام التحديث
    const { setClause, values } = prepareUpdateQuery(cleanedData);

    const [result] = await pool.promise().query(
      `UPDATE extra_hours SET ${setClause} WHERE id = ?`,
      [...values, extraHourId]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'السجل غير موجود' });
    }

    // إنشاء رسالة التعديل المفصلة
    const fieldLabels = {
      employee_code: 'كود الموظف',
      employee_name: 'اسم الموظف',
      department: 'الإدارة',
      extra_hours: 'عدد الساعات',
      extra_date: 'تاريخ الإضافي',
      notes: 'الملاحظات'
    };

    const editMessage = createEditMessage(
      `ساعات إضافية للموظف: ${employeeName} (كود: ${employee_code})`,
      oldData,
      newData,
      fieldLabels
    );

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'edit',
      module: 'extra_hours',
      record_id: extraHourId.toString(),
      message: editMessage
    });

    res.json({ message: 'تم تحديث الساعات الإضافية بنجاح' });
  } catch (error) {
    console.error('خطأ في تحديث الساعات الإضافية:', error);
    res.status(500).json({ error: 'فشل في تحديث الساعات الإضافية' });
  }
});

// حذف ساعات إضافية
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const extraHourId = req.params.id;

    // الحصول على معلومات السجل قبل الحذف
    const [extraHourData] = await pool.promise().query(
      'SELECT employee_code, employee_name, extra_hours, extra_date FROM extra_hours WHERE id = ?',
      [extraHourId]
    );

    if (extraHourData.length === 0) {
      return res.status(404).json({ error: 'السجل غير موجود' });
    }

    const extraHour = extraHourData[0];

    // حذف السجل
    const [result] = await pool.promise().query(
      'DELETE FROM extra_hours WHERE id = ?',
      [extraHourId]
    );

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'delete',
      module: 'extra_hours',
      record_id: extraHourId.toString(),
      message: `تم حذف ساعات إضافية للموظف: ${extraHour.employee_name} (كود: ${extraHour.employee_code}) - عدد الساعات: ${extraHour.extra_hours} ساعة - التاريخ: ${extraHour.extra_date}`
    });

    res.json({ message: 'تم حذف الساعات الإضافية بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف الساعات الإضافية:', error);
    res.status(500).json({ error: 'فشل في حذف الساعات الإضافية' });
  }
});

// الحصول على الساعات الإضافية حسب الموظف
router.get('/employee/:code', async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const { code } = req.params;
    
    const [rows] = await pool.promise().query(`
      SELECT 
        eh.*,
        DATE_FORMAT(eh.extra_date, '%Y-%m-%d') as extra_date_formatted
      FROM extra_hours eh
      WHERE eh.employee_code = ?
      ORDER BY eh.created_at DESC, eh.id DESC
    `, [code]);
    
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب ساعات الموظف الإضافية:', error);
    res.status(500).json({ error: 'فشل في جلب بيانات ساعات الموظف الإضافية' });
  }
});

// الحصول على الساعات الإضافية حسب الإدارة
router.get('/department/:department', async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const { department } = req.params;
    
    const [rows] = await pool.promise().query(`
      SELECT 
        eh.*,
        DATE_FORMAT(eh.extra_date, '%Y-%m-%d') as extra_date_formatted
      FROM extra_hours eh
      WHERE eh.department = ?
      ORDER BY eh.created_at DESC, eh.id DESC
    `, [department]);
    
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب ساعات الإدارة الإضافية:', error);
    res.status(500).json({ error: 'فشل في جلب بيانات ساعات الإدارة الإضافية' });
  }
});

// الحصول على الساعات الإضافية حسب نطاق التاريخ
router.get('/date-range', async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const { start_date, end_date } = req.query;

    if (!start_date || !end_date) {
      return res.status(400).json({ error: 'يجب تحديد تاريخ البداية والنهاية' });
    }

    const [rows] = await pool.promise().query(`
      SELECT
        eh.*,
        DATE_FORMAT(eh.extra_date, '%Y-%m-%d') as extra_date_formatted
      FROM extra_hours eh
      WHERE eh.extra_date BETWEEN ? AND ?
      ORDER BY eh.created_at DESC, eh.id DESC
    `, [start_date, end_date]);

    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب الساعات الإضافية حسب التاريخ:', error);
    res.status(500).json({ error: 'فشل في جلب بيانات الساعات الإضافية' });
  }
});

// البحث في الساعات الإضافية
router.get('/search', async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    await createExtraHoursTable(pool);

    const {
      employee_code,
      employee_name,
      department,
      start_date,
      end_date,
      min_hours,
      max_hours
    } = req.query;

    let query = `
      SELECT
        eh.*,
        DATE_FORMAT(eh.extra_date, '%Y-%m-%d') as extra_date_formatted,
        CASE
          WHEN e.full_name IS NOT NULL THEN e.full_name
          ELSE eh.employee_name
        END as current_employee_name,
        CASE
          WHEN e.department IS NOT NULL THEN e.department
          ELSE eh.department
        END as current_department
      FROM extra_hours eh
      LEFT JOIN employees e ON eh.employee_code = e.code
      WHERE 1=1
    `;
    const params = [];

    // البحث بكود الموظف
    if (employee_code) {
      query += " AND eh.employee_code = ?";
      params.push(employee_code);
    }

    // البحث باسم الموظف
    if (employee_name) {
      query += " AND (eh.employee_name LIKE ? OR e.full_name LIKE ?)";
      params.push(`%${employee_name}%`, `%${employee_name}%`);
    }

    // البحث بالإدارة
    if (department) {
      query += " AND (eh.department = ? OR e.department = ?)";
      params.push(department, department);
    }

    // البحث بنطاق التاريخ
    if (start_date) {
      query += " AND eh.extra_date >= ?";
      params.push(start_date);
    }

    if (end_date) {
      query += " AND eh.extra_date <= ?";
      params.push(end_date);
    }

    // البحث بنطاق الساعات
    if (min_hours) {
      query += " AND eh.extra_hours >= ?";
      params.push(min_hours);
    }

    if (max_hours) {
      query += " AND eh.extra_hours <= ?";
      params.push(max_hours);
    }

    query += " ORDER BY eh.created_at DESC, eh.id DESC";

    const [rows] = await pool.promise().query(query, params);
    res.json(rows);
  } catch (error) {
    console.error('خطأ في البحث في الساعات الإضافية:', error);
    res.status(500).json({ error: 'فشل في البحث في الساعات الإضافية' });
  }
});

// DataTables server-side processing للساعات الإضافية المضافة
router.get('/datatables', authenticateToken, addDepartmentFilter, async (req, res) => {
  try {
    const pool = req.app.locals.pool;

    // التأكد من وجود الجدول
    try {
      await createExtraHoursTable(pool);
    } catch (tableError) {
      console.error('Error creating table:', tableError);
    }

    // معاملات DataTables
    const draw = parseInt(req.query.draw) || 1;
    const start = parseInt(req.query.start) || 0;
    const length = parseInt(req.query.length) || 10;
    const searchValue = req.query.search?.value || '';
    const orderColumnIndex = parseInt(req.query.order?.[0]?.column) || 0;
    const orderDirection = req.query.order?.[0]?.dir || 'desc';

    // أعمدة الجدول (حسب ترتيب الظهور في HTML)
    const columns = [
      'id',
      'employee_code',
      'employee_name',
      'department',
      'extra_hours',
      'extra_date',
      'notes'
    ];

    const orderColumn = columns[orderColumnIndex] || 'id';

    // بناء استعلام العد الإجمالي مع فلترة الإدارات
    let totalRecords = 0;
    try {
      let totalCountWhereClause = 'WHERE 1=1';
      let totalCountParams = [];
      const totalDepartmentFilter = addDepartmentFilterToExtraHours(totalCountWhereClause, totalCountParams, req);

      const countQuery = `SELECT COUNT(*) as total FROM extra_hours ${totalDepartmentFilter.whereClause.replace('department', 'department')}`;
      const [countResult] = await pool.promise().query(countQuery, totalDepartmentFilter.queryParams);
      totalRecords = countResult[0].total;
    } catch (countError) {
      console.error('Error counting records:', countError);
      totalRecords = 0;
    }

    console.log('DataTables request params:', {
      draw, start, length, searchValue, orderColumnIndex, orderDirection, orderColumn
    });
    console.log('Advanced search params:', req.query);
    console.log('Total records in extra_hours:', totalRecords);

    // إذا لم توجد بيانات، أرجع استجابة فارغة
    if (totalRecords === 0) {
      return res.json({
        draw: draw,
        recordsTotal: 0,
        recordsFiltered: 0,
        data: []
      });
    }

    // بناء استعلام البحث والفلترة
    let searchQuery = `
      SELECT
        eh.id,
        eh.employee_code,
        eh.employee_name,
        eh.department,
        eh.extra_hours,
        DATE_FORMAT(eh.extra_date, '%Y-%m-%d') as extra_date_formatted,
        eh.notes,
        eh.created_at
      FROM extra_hours eh
      WHERE 1=1
    `;
    let searchParams = [];

    // إضافة فلترة الإدارات
    let whereClause = 'WHERE 1=1';
    let queryParams = [];
    const departmentFilter = addDepartmentFilterToExtraHours(whereClause, queryParams, req);

    if (departmentFilter.whereClause !== 'WHERE 1=1') {
      const additionalWhere = departmentFilter.whereClause.replace('WHERE 1=1', '');
      searchQuery += additionalWhere.replace('department', 'eh.department');
      searchParams.push(...departmentFilter.queryParams);
    }

    // البحث العام
    if (searchValue) {
      searchQuery += ` AND (
        eh.employee_code LIKE ? OR
        eh.employee_name LIKE ? OR
        eh.department LIKE ? OR
        eh.notes LIKE ? OR
        CAST(eh.extra_hours AS CHAR) LIKE ?
      )`;
      const searchPattern = `%${searchValue}%`;
      searchParams.push(searchPattern, searchPattern, searchPattern, searchPattern, searchPattern);
    }

    // البحث المتقدم من معاملات إضافية
    if (req.query.extraHourId) {
      searchQuery += " AND eh.id = ?";
      searchParams.push(req.query.extraHourId);
    }

    if (req.query.employeeCode) {
      searchQuery += " AND eh.employee_code LIKE ?";
      searchParams.push(`%${req.query.employeeCode}%`);
    }

    if (req.query.employeeName) {
      searchQuery += " AND eh.employee_name LIKE ?";
      searchParams.push(`%${req.query.employeeName}%`);
    }

    if (req.query.department) {
      searchQuery += " AND eh.department LIKE ?";
      searchParams.push(`%${req.query.department}%`);
    }

    if (req.query.minHours) {
      searchQuery += " AND eh.extra_hours >= ?";
      searchParams.push(req.query.minHours);
    }

    if (req.query.maxHours) {
      searchQuery += " AND eh.extra_hours <= ?";
      searchParams.push(req.query.maxHours);
    }

    if (req.query.dateFrom) {
      searchQuery += " AND eh.extra_date >= ?";
      searchParams.push(req.query.dateFrom);
    }

    if (req.query.dateTo) {
      searchQuery += " AND eh.extra_date <= ?";
      searchParams.push(req.query.dateTo);
    }

    // حساب عدد السجلات المفلترة - بناء استعلام منفصل
    let countQuery = `
      SELECT COUNT(*) as total
      FROM extra_hours eh
      WHERE 1=1
    `;

    // إضافة نفس شروط البحث
    if (searchValue) {
      countQuery += ` AND (
        eh.employee_code LIKE ? OR
        eh.employee_name LIKE ? OR
        eh.department LIKE ? OR
        eh.notes LIKE ? OR
        CAST(eh.extra_hours AS CHAR) LIKE ?
      )`;
    }

    if (req.query.extraHourId) {
      countQuery += " AND eh.id = ?";
    }

    if (req.query.employeeCode) {
      countQuery += " AND eh.employee_code LIKE ?";
    }

    if (req.query.employeeName) {
      countQuery += " AND eh.employee_name LIKE ?";
    }

    if (req.query.department) {
      countQuery += " AND eh.department LIKE ?";
    }

    if (req.query.minHours) {
      countQuery += " AND eh.extra_hours >= ?";
    }

    if (req.query.maxHours) {
      countQuery += " AND eh.extra_hours <= ?";
    }

    if (req.query.dateFrom) {
      countQuery += " AND eh.extra_date >= ?";
    }

    if (req.query.dateTo) {
      countQuery += " AND eh.extra_date <= ?";
    }

    const [filteredCountResult] = await pool.promise().query(countQuery, searchParams);
    const filteredRecords = filteredCountResult[0].total;

    // إضافة الترتيب والحد - التحقق من صحة عمود الترتيب
    const validColumns = ['id', 'employee_code', 'employee_name', 'department', 'extra_hours', 'extra_date', 'notes'];
    const safeOrderColumn = validColumns.includes(orderColumn) ? orderColumn : 'id';
    const safeOrderDirection = ['asc', 'desc'].includes(orderDirection.toLowerCase()) ? orderDirection.toUpperCase() : 'DESC';

    searchQuery += ` ORDER BY eh.${safeOrderColumn} ${safeOrderDirection}`;
    searchQuery += ` LIMIT ${parseInt(length)} OFFSET ${parseInt(start)}`;

    // تنفيذ الاستعلام
    console.log('Final search query:', searchQuery);
    console.log('Search params:', searchParams);

    const [rows] = await pool.promise().query(searchQuery, searchParams);
    console.log('Query results count:', rows.length);

    // تنسيق البيانات لـ DataTables
    const formattedData = rows.map(row => {
      return [
        row.id,
        row.employee_code,
        row.employee_name,
        row.department,
        parseFloat(row.extra_hours) % 1 === 0 ? parseInt(row.extra_hours) : parseFloat(row.extra_hours).toFixed(1),
        row.extra_date_formatted,
        row.notes || '-',
        `<button class="edit-added-btn" data-extra-id="${row.id}" data-permission="can_edit">تعديل</button>
         <button class="delete-added-btn" data-extra-id="${row.id}" data-permission="can_delete">حذف</button>`
      ];
    });

    // إرجاع البيانات بتنسيق DataTables
    res.json({
      draw: draw,
      recordsTotal: totalRecords,
      recordsFiltered: filteredRecords,
      data: formattedData
    });

  } catch (error) {
    console.error('خطأ في جلب بيانات DataTables:', error);
    console.error('Stack trace:', error.stack);
    console.error('Request query:', req.query);
    res.status(500).json({
      error: 'فشل في جلب البيانات',
      message: error.message,
      draw: req.query.draw || 1,
      recordsTotal: 0,
      recordsFiltered: 0,
      data: []
    });
  }
});

module.exports = router;
