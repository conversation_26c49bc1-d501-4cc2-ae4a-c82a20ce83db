/* تنسيقات خاصة بصفحة الإجازات */
/* تطبيق تنسيقات بنفسجية مطابقة لصفحة المساهمات */
/* إصلاح التداخل مع shared-styles.css باستخدام !important */
@import url('shared-styles.css');

/* تنسيقات مساعدات التواريخ */
.date-helper {
  display: block;
  color: #666;
  font-size: 0.85em;
  margin-top: 4px;
  font-style: italic;
  background-color: rgba(103, 58, 183, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  border-left: 3px solid #673ab7;
}

/* تنسيق زر الحذف */
.delete-vacation-btn {
  background-color: #ff3333;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;

}

.delete-vacation-btn:hover {
  background-color: #cc0000;
}

/* تم نقل تنسيقات التبويبات إلى ملف shared-styles.css */

/* تنسيق نموذج الإجازة - خلفية بنفسجية غامقة طبيعية */
.vacation-form {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 15px;
  margin-bottom: 20px;
  padding: 25px;
  background: linear-gradient(135deg, #e8e3f3 0%, #d1c4e9 100%);
  border: 2px solid #673ab7;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(103, 58, 183, 0.15);
  color: #333;
  position: relative;
}

/* إضافة تأثير بنفسجي غامق طبيعي للنموذج */
.vacation-form::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #673ab7, #512da8, #673ab7);
  border-radius: 12px;
  z-index: -1;
  opacity: 0.3;
}

/* تحسين مظهر الحقول داخل النموذج */
.vacation-form .form-group {
  background: rgba(255, 255, 255, 0.9) !important;
  padding: 15px !important;
  border-radius: 8px !important;
  border: 1px solid rgba(103, 58, 183, 0.2) !important;
  transition: all 0.3s ease !important;
  margin-bottom: 0 !important;
}

.vacation-form .form-group:hover {
  background: rgba(255, 255, 255, 0.95) !important;
  border-color: #673ab7 !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 8px rgba(103, 58, 183, 0.1) !important;
}

/* الحقول المغلقة (readonly) - خلفية بنفسجية خفيفة */
.vacation-form .form-group:has(input[readonly]),
.vacation-form .form-group:has(input:read-only) {
  background: rgba(243, 229, 245, 0.8);
  border: 1px solid rgba(103, 58, 183, 0.3);
}

.vacation-form .form-group:has(input[readonly]):hover,
.vacation-form .form-group:has(input:read-only):hover {
  background: rgba(243, 229, 245, 0.95);
  border-color: #673ab7;
  box-shadow: 0 4px 8px rgba(103, 58, 183, 0.1);
}

/* تحسين مظهر التسميات */
.vacation-form .form-group label {
  color: #512da8 !important;
  font-weight: 600 !important;
  margin-bottom: 8px !important;
  display: block !important;
  font-size: 14px !important;
}

/* تحسين مظهر الحقول */
.vacation-form input,
.vacation-form select,
.vacation-form textarea {
  border: 2px solid #e0e0e0 !important;
  border-radius: 6px !important;
  padding: 10px !important;
  transition: all 0.3s ease !important;
  background: white !important;
  width: 100% !important;
  box-sizing: border-box !important;
  font-size: 14px !important;
}

.vacation-form input:focus,
.vacation-form select:focus,
.vacation-form textarea:focus {
  border-color: #673ab7 !important;
  box-shadow: 0 0 0 3px rgba(103, 58, 183, 0.1) !important;
  outline: none !important;
  transform: none !important;
}

/* الحقول المغلقة (readonly) - خلفية بنفسجية خفيفة */
.vacation-form input[readonly],
.vacation-form input:read-only,
.vacation-form select[readonly],
.vacation-form select:read-only {
  background: rgba(243, 229, 245, 0.7) !important;
  border-color: rgba(103, 58, 183, 0.4) !important;
  color: #512da8 !important;
  cursor: not-allowed !important;
}

.vacation-form input[readonly]:focus,
.vacation-form input:read-only:focus,
.vacation-form select[readonly]:focus,
.vacation-form select:read-only:focus {
  border-color: #673ab7 !important;
  box-shadow: 0 0 0 3px rgba(103, 58, 183, 0.1) !important;
  transform: none !important;
}

/* الحقول العادية - خلفية بيضاء نقية */
.vacation-form input:not([readonly]):not(:read-only),
.vacation-form select:not([readonly]):not(:read-only),
.vacation-form textarea:not([readonly]):not(:read-only) {
  background: #ffffff !important;
}

/* تنسيق عنوان قسم إضافة الإجازة */
.add-vacation-title {
  background: linear-gradient(135deg, #673ab7, #512da8);
  color: white;
  padding: 15px 25px;
  border-radius: 8px 8px 0 0;
  margin: 0 0 0 0;
  font-size: 1.2em;
  font-weight: bold;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* تنسيق حاوي قسم إضافة الإجازة */
.add-vacation-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
  overflow: hidden;
}

.form-actions {
  grid-column: span 4;
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 15px;
}

/* تحسين زر الحفظ */
.vacation-form .save-btn {
  background: linear-gradient(135deg, #673ab7, #512da8);
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 8px;
  font-size: 1.1em;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 8px rgba(103, 58, 183, 0.3);
}

.vacation-form .save-btn:hover {
  background: linear-gradient(135deg, #512da8, #673ab7) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 12px rgba(103, 58, 183, 0.4) !important;
}

/* تأكيد إضافي للتنسيقات البنفسجية */
#add-vacation .vacation-form {
  background: linear-gradient(135deg, #e8e3f3 0%, #d1c4e9 100%) !important;
  border: 2px solid #673ab7 !important;
}

#add-vacation .add-vacation-title {
  background: linear-gradient(135deg, #673ab7, #512da8) !important;
  color: white !important;
}

/* إزالة التنسيقات المتداخلة من shared-styles */
.vacation-form .form-group input,
.vacation-form .form-group select,
.vacation-form .form-group textarea {
  background-color: white !important;
  border: 2px solid #e0e0e0 !important;
  padding: 10px !important;
}

.vacation-form .form-group input[readonly],
.vacation-form .form-group select[readonly] {
  background-color: rgba(243, 229, 245, 0.7) !important;
  border-color: rgba(103, 58, 183, 0.4) !important;
  color: #512da8 !important;
}

/* تنسيق جدول الإجازات */
.vacations-table-container {
  margin-top: 20px;
  overflow-x: auto;
}

/* تنسيق الصف المميز (آخر إضافة) */
.added-vacations-table tr:first-child {
  background-color: #e8f5e8 !important;
  border: 2px solid #4CAF50;
}

.added-vacations-table tr:first-child td {
  font-weight: bold;
}

.table-controls {
  margin-bottom: 10px;
  display: flex;
  justify-content: flex-end;
}

/* تم نقل تنسيقات الأزرار إلى shared-styles.css */

/* تم نقل تنسيقات .vacations-table إلى shared-styles.css */

/* تنسيق جدول الإجازات المضافة */
.added-vacations-container {
  margin-top: 20px;
  overflow-x: auto;
}

/* تم نقل تنسيقات .added-vacations-table إلى shared-styles.css */

.added-vacations-container h3 {
  margin-top: 15px;
  margin-bottom: 10px;
  color: #333;
  font-weight: bold;
  font-size: 18px;
  text-align: right;
}

.delete-added-btn {
  background-color: #ff3333;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
  margin-right: 5px;

}

.delete-added-btn:hover {
  background-color: #cc0000;
}

.edit-added-btn {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
  margin-right: 5px;

}

.edit-added-btn:hover {
  background-color: #0056b3;
}

/* تنسيق فلاتر البحث */
.search-container {
  margin-bottom: 20px;
}

.search-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-items: flex-end;
}

.date-filter {
  display: flex;
  flex-direction: column;
}

.date-filter label,
.name-filter label {
  margin-bottom: 5px;
  font-weight: bold;
}

.date-filter input,
.name-filter input {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.name-filter {
  display: flex;
  flex-direction: column;
  min-width: 200px;
}

.search-filters select {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

/* تم نقل تنسيقات الأزرار إلى shared-styles.css */

/* تنسيق النافذة المنبثقة لتعديل الإجازة المضافة */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.4);
}

.modal-content {
  background-color: #fefefe;
  margin: 5% auto;
  padding: 20px;
  border: 1px solid #888;
  border-radius: 8px;
  width: 80%;
  max-width: 600px;
  position: relative;
  direction: rtl;
  text-align: right;
}

.close-modal {
  color: #aaa;
  float: left;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
  position: absolute;
  top: 10px;
  left: 15px;
}

.close-modal:hover,
.close-modal:focus {
  color: black;
  text-decoration: none;
}

.modal h2 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
  text-align: center;
}

/* تنسيق أزرار النموذج في النافذة المنبثقة */
.modal .form-actions {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 20px;
}

.modal .save-btn,
.modal .cancel-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.modal .save-btn {
  background-color: #28a745;
  color: white;
}

.modal .save-btn:hover {
  background-color: #218838;
}

.modal .cancel-btn {
  background-color: #6c757d;
  color: white;
}

.modal .cancel-btn:hover {
  background-color: #545b62;
}

/* تنسيق قسم التقارير - فئات خاصة لتقارير الإجازات */
.vacation-reports-container {
  padding: 25px;
  background-color: #ffffff;
  border-radius: 12px;
  margin: 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.vacation-reports-container h2 {
  text-align: center;
  color: var(--primary-color);
  margin-bottom: 25px;
  font-size: 28px;
  font-weight: 700;
  padding-bottom: 15px;
  border-bottom: 3px solid var(--primary-color);
  position: relative;
}

.vacation-reports-container h2::before {
  content: "📊";
  margin-left: 10px;
  font-size: 32px;
}

/* تنسيق تبويبات التقارير */
.reports-tabs {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 12px;
  margin-bottom: 30px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 10px;
  border: 1px solid #e9ecef;
}

.report-tab-btn {
  background-color: #ffffff;
  color: #495057;
  border: 2px solid #e9ecef;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
  white-space: nowrap;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  flex: 0 1 auto;
  min-width: 200px;
}

.report-tab-btn:hover {
  background-color: #e3f2fd;
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.15);
}

.report-tab-btn.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  box-shadow: 0 6px 20px rgba(25, 118, 210, 0.3);
  transform: translateY(-2px);
}

/* تنسيق محتوى التقارير */
.report-content {
  background-color: #ffffff;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  margin-top: 20px;
}

/* تنسيق فلاتر التقارير - فئات خاصة لتقارير الإجازات */
.vacation-report-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 30px;
  align-items: end;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  padding: 25px;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.vacation-filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 150px;
  flex: 1;
}

.vacation-filter-group label {
  font-weight: 600;
  color: var(--primary-color);
  font-size: 14px;
  margin-bottom: 5px;
}

.vacation-filter-group input,
.vacation-filter-group select {
  padding: 12px 15px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 14px;
  background-color: white !important;
  color: #333 !important;
  font-weight: 500 !important;
  transition: all 0.3s ease;
  font-family: inherit;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.vacation-filter-group input:focus,
.vacation-filter-group select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
  transform: translateY(-1px);
}

.vacation-filter-group input:hover:not(:focus),
.vacation-filter-group select:hover:not(:focus) {
  border-color: #c1d4ed;
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.08);
}

.vacation-filter-group select option {
  color: #000 !important;
  background-color: white !important;
  padding: 8px;
  font-weight: normal !important;
}

.vacation-filter-group select {
  color: #000 !important;
  font-weight: bold !important;
}

/* تحسينات خاصة لقوائم الإدارات */
#employeeSummaryDept,
#absenceDepartment {
  color: #000 !important;
  background-color: white !important;
  font-weight: bold !important;
  font-size: 14px !important;
}

#employeeSummaryDept option,
#absenceDepartment option {
  color: #000 !important;
  background-color: white !important;
  font-weight: normal !important;
  padding: 8px !important;
  opacity: 1 !important;
  text-shadow: none !important;
}

/* إزالة أي تأثيرات قد تخفي النص */
select:disabled,
select[disabled] {
  color: #000 !important;
  opacity: 1 !important;
}

/* تأكيد وضوح النص في جميع الحالات */
.vacation-filter-group select,
.vacation-filter-group select option {
  -webkit-text-fill-color: #000 !important;
  text-fill-color: #000 !important;
}

/* تنسيق أزرار التقارير */
.generate-btn {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 600;
  transition: all 0.3s ease;
  height: fit-content;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
  flex: 0 0 auto;
}

.generate-btn:hover {
  background: linear-gradient(135deg, #218838, #1e7e34);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.export-btn {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: #ffffff;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 600;
  transition: all 0.3s ease;
  height: fit-content;
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
  flex: 0 0 auto;
}

.export-btn:hover {
  background: linear-gradient(135deg, var(--primary-hover), var(--primary-dark));
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(25, 118, 210, 0.4);
}

.print-btn {
  background-color: #6f42c1;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  height: fit-content;
}

.print-btn:hover {
  background-color: #5a32a3;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(111, 66, 193, 0.3);
}

/* تنسيق جداول التقارير */
.report-table-container {
  overflow-x: auto;
  margin-top: 20px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.report-table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  font-size: 14px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  margin-top: 25px;
}

.report-table th {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: white;
  padding: 16px 12px;
  text-align: center;
  font-weight: 700;
  border-bottom: none;
  position: sticky;
  top: 0;
  z-index: 10;
  font-size: 15px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  letter-spacing: 0.5px;
}

.report-table td {
  padding: 14px 12px;
  text-align: center;
  border-bottom: 1px solid #e9ecef;
  transition: all 0.3s ease;
  font-weight: 500;
}

.report-table tbody tr {
  transition: all 0.3s ease;
}

.report-table tbody tr:hover {
  background-color: #e3f2fd;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.1);
}

.report-table tbody tr:nth-child(even) {
  background-color: #f8fafc;
}

/* تنسيق بطاقة معلومات الموظف */
.employee-info-card {
  background-color: #e3f2fd;
  border: 1px solid #bbdefb;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 25px;
}

.employee-info-card h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #1976d2;
  text-align: center;
  font-size: 18px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: white;
  padding: 10px 15px;
  border-radius: 4px;
  border: 1px solid #e3f2fd;
}

.info-item label {
  font-weight: 600;
  color: #424242;
}

.info-item span {
  font-weight: 500;
  color: #1976d2;
}

/* تنسيق متجاوب للتقارير */
@media (max-width: 1200px) {
  .vacation-form {
    grid-template-columns: 1fr 1fr 1fr;
  }
  .form-actions {
    grid-column: span 3;
  }
}

@media (max-width: 900px) {
  .vacation-form {
    grid-template-columns: 1fr 1fr;
  }
  .form-actions {
    grid-column: span 2;
  }
}

@media (max-width: 768px) {
  /* تنسيقات النموذج */
  .vacation-form {
    grid-template-columns: 1fr;
  }
  .form-actions {
    grid-column: span 1;
  }

  /* تنسيقات النافذة المنبثقة */
  .modal-content {
    width: 95%;
    margin: 2% auto;
    padding: 15px;
    max-height: 95vh;
  }

  /* تنسيقات خاصة لنافذة تفاصيل الإجازات على الشاشات الصغيرة */
  #vacationDetailsModal .modal-content {
    width: 95%;
    padding: 20px;
  }

  #vacationDetailsModal #employeeInfo {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  #vacationDetailsModal .vacation-balance-info {
    grid-column: span 1;
  }

  .vacation-balance-cards {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .balance-card {
    padding: 15px;
  }

  .balance-card-value {
    font-size: 24px;
  }

  #vacationDetailsModal .details-actions {
    flex-direction: column;
    gap: 10px;
  }

  #editAddedVacationModal .modal-content {
    width: 95%;
  }

  #editAddedVacationModal .vacation-form {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  #editAddedVacationModal .form-actions {
    flex-direction: column;
    gap: 10px;
  }

  #editAddedVacationModal .save-btn,
  #editAddedVacationModal .cancel-btn {
    width: 100%;
    padding: 15px;
  }

  #editAddedVacationModal h2 {
    font-size: 18px;
    padding-right: 30px;
  }

  /* تنسيقات الجداول */
  .vacation-table th,
  .vacation-table td {
    padding: 8px 4px;
    font-size: 12px;
  }

  .action-btn {
    padding: 6px 8px;
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .modal-content {
    width: 98%;
    margin: 1% auto;
    padding: 10px;
  }

  #editAddedVacationModal .form-group input,
  #editAddedVacationModal .form-group select {
    padding: 10px;
    font-size: 16px; /* منع التكبير على iOS */
  }
}

/* تنسيق أزرار التحكم بالسيرفر */
.server-controls {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.status-btn {
  padding: 8px 15px;
  border: none;
  border-radius: 4px;
  font-weight: bold;
  cursor: default;
}

.status-btn.connected {
  background-color: #4CAF50;
  color: white;
}

.status-btn.disconnected {
  background-color: #f44336;
  color: white;
}

.control-btn {
  background-color: #2196F3;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
}

/* تنسيق زر عرض التفاصيل */
.details-btn {
  background-color: #673AB7;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9em;
}

.details-btn:hover {
  background-color: #5E35B1;
}

/* تنسيق رسالة عدم وجود نتائج */
.no-results {
  text-align: center;
  padding: 20px;
  color: #666;
  font-style: italic;
}

/* تنسيق النافذة المنبثقة */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  overflow-y: auto;
}

.modal-content {
  background-color: #fff;
  margin: 5% auto;
  padding: 25px;
  border-radius: 8px;
  width: 70%;
  max-width: 800px;
  position: relative;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-height: 90vh;
  overflow-y: auto;
}

/* تنسيق خاص لنافذة تعديل الإجازة */
#editAddedVacationModal .modal-content {
  width: 80%;
  max-width: 900px;
}

#editAddedVacationModal .vacation-form {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

#editAddedVacationModal .form-group {
  display: flex;
  flex-direction: column;
}

#editAddedVacationModal .form-group label {
  font-weight: bold;
  margin-bottom: 8px;
  color: #333;
  font-size: 14px;
}

#editAddedVacationModal .form-group input,
#editAddedVacationModal .form-group select {
  padding: 12px;
  border: 2px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

#editAddedVacationModal .form-group input:focus,
#editAddedVacationModal .form-group select:focus {
  outline: none;
  border-color: #4CAF50;
  box-shadow: 0 0 5px rgba(76, 175, 80, 0.3);
}

#editAddedVacationModal .form-group input[readonly] {
  background-color: #f5f5f5;
  color: #666;
  cursor: not-allowed;
}

#editAddedVacationModal .form-actions {
  grid-column: 1 / -1;
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 25px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

#editAddedVacationModal .save-btn,
#editAddedVacationModal .cancel-btn {
  padding: 12px 25px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

#editAddedVacationModal .save-btn {
  background-color: #4CAF50;
  color: white;
}

#editAddedVacationModal .save-btn:hover {
  background-color: #45a049;
  transform: translateY(-1px);
}

#editAddedVacationModal .cancel-btn {
  background-color: #f44336;
  color: white;
}

#editAddedVacationModal .cancel-btn:hover {
  background-color: #da190b;
  transform: translateY(-1px);
}

/* تنسيق النافذة المنبثقة لتفاصيل الإجازات - حجم أكبر وأعرض */
#vacationDetailsModal .modal-content {
  max-width: 1200px;
  width: 90%;
  position: relative;
  min-height: 600px;
  padding: 30px;
}

/* تحسين تخطيط معلومات الموظف في النافذة الكبيرة */
#vacationDetailsModal #employeeInfo {
  margin-bottom: 25px;
}

#vacationDetailsModal #employeeInfo > p {
  display: inline-block;
  margin: 0 20px 10px 0;
  padding: 10px 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #3498db;
}

/* تنسيق بطاقات رصيد الإجازات الجميلة */
#vacationDetailsModal .vacation-balance-info {
  margin-top: 20px;
}

#vacationDetailsModal .vacation-balance-info h3 {
  margin-bottom: 20px;
  color: #2c3e50;
  font-size: 18px;
  text-align: center;
}

.vacation-balance-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 15px;
}

.balance-card {
  background: #ffffff;
  color: #2c3e50;
  padding: 25px;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  border: 1px solid #e9ecef;
  overflow: hidden;
}

.balance-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3498db, #2ecc71);
  pointer-events: none;
}

.balance-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.12);
}

.balance-card.total::before {
  background: linear-gradient(90deg, #3498db, #5dade2);
}

.balance-card.used::before {
  background: linear-gradient(90deg, #e74c3c, #f1948a);
}

.balance-card.remaining::before {
  background: linear-gradient(90deg, #2ecc71, #58d68d);
}

.balance-card-icon {
  font-size: 32px;
  margin-bottom: 15px;
  opacity: 0.8;
}

.balance-card.total .balance-card-icon {
  color: #3498db;
}

.balance-card.used .balance-card-icon {
  color: #e74c3c;
}

.balance-card.remaining .balance-card-icon {
  color: #2ecc71;
}

.balance-card-label {
  font-size: 15px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #7f8c8d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.balance-card-value {
  font-size: 36px;
  font-weight: 700;
  margin: 0;
  color: #2c3e50;
  line-height: 1;
}

/* تحسين عرض الجدول في النافذة الكبيرة */
#vacationDetailsModal .vacations-details-table {
  font-size: 14px;
  width: 100%;
}

#vacationDetailsModal .vacations-details-table th,
#vacationDetailsModal .vacations-details-table td {
  padding: 12px 8px;
  text-align: center;
}

#vacationDetailsModal .vacations-details-table th {
  background-color: #f1f3f4;
  font-weight: bold;
  font-size: 15px;
}

/* تحسين أزرار الإجراءات */
#vacationDetailsModal .details-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20px 0;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.close,
.close-modal {
  position: absolute;
  top: 15px;
  right: 20px;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
  color: #999;
  transition: color 0.3s ease;
  z-index: 1001;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: transparent;
}

.close:hover,
.close-modal:hover {
  color: #f44336;
  background-color: #f5f5f5;
}

/* تنسيق عنوان النافذة المنبثقة */
#editAddedVacationModal h2 {
  margin: 0 0 20px 0;
  padding: 0 40px 15px 0;
  border-bottom: 2px solid #4CAF50;
  color: #333;
  font-size: 20px;
  font-weight: bold;
}

.close-details-btn {
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: #f44336;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
}

.details-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.print-btn,
.export-details-btn {
  background-color: #2196F3;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
}

.print-btn:hover,
.export-details-btn:hover {
  background-color: #0b7dda;
}

.server-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 20px;
}

.server-form input {
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.server-form button {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 10px;
  border-radius: 4px;
  cursor: pointer;
}

/* تنسيق معلومات الموظف في نافذة التفاصيل */
#employeeInfo {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 15px;
  border: 1px solid #eee;
}

/* تنسيق جدول تفاصيل الإجازات */
.vacations-details-container {
  max-height: 400px;
  overflow-y: auto;
}

/* تم نقل تنسيقات .vacations-details-table إلى shared-styles.css */

/* تنسيق قسم معلومات رصيد الإجازات */
.vacation-balance-info {
  margin-top: 15px;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 5px;
  border-right: 4px solid #4caf50;
}

.vacation-balance-info h3 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #333;
  font-size: 16px;
}

.vacation-balance-info p {
  margin: 5px 0;
}

#detailsLeaveBalance, #detailsLeaveUsed, #detailsLeaveRemaining {
  font-weight: bold;
}

#detailsLeaveBalance {
  color: #2196F3; /* أزرق */
}

#detailsLeaveUsed {
  color: #F44336; /* أحمر */
}

#detailsLeaveRemaining {
  color: #4CAF50; /* أخضر */
}





.separator {
    margin: 0 15px;
}

.danger-btn {
    background-color: #dc3545;
    border-color: #dc3545;
    margin-left: 20px;
}

.danger-btn:hover {
    background-color: #c82333;
    border-color: #bd2130;
    transform: translateY(-1px);
}

.edit-btn {
    background-color: #007bff;
    color: white;
    border: 1px solid #007bff;
    padding: 5px 10px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    margin-right: 5px;
    transition: all 0.3s ease;
}

.edit-btn:hover {
    background-color: #0056b3;
    border-color: #004085;
    transform: translateY(-1px);
}

/* تنسيقات متجاوبة للتقارير */
@media (max-width: 768px) {
  .vacation-reports-container {
    padding: 15px;
    margin: 0;
  }

  .vacation-reports-container h2 {
    font-size: 22px;
    margin-bottom: 20px;
  }

  .reports-tabs {
    flex-direction: column;
    gap: 10px;
    padding: 10px;
  }

  .report-tab-btn {
    padding: 12px 15px;
    font-size: 12px;
    min-width: auto;
    width: 100%;
  }

  .vacation-report-filters {
    flex-direction: column;
    gap: 15px;
    padding: 20px;
  }

  .vacation-filter-group {
    min-width: auto;
    width: 100%;
  }

  .vacation-filter-group input,
  .vacation-filter-group select {
    padding: 10px 12px;
    font-size: 13px;
  }

  .generate-btn,
  .export-btn {
    padding: 10px 18px;
    font-size: 12px;
    width: 100%;
    margin-top: 10px;
  }

  .report-table {
    font-size: 12px;
    margin-top: 20px;
  }

  .report-table th,
  .report-table td {
    padding: 10px 6px;
    font-size: 11px;
  }

  .report-table th {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .vacation-reports-container h2 {
    font-size: 18px;
    flex-direction: column;
    gap: 5px;
  }

  .vacation-report-filters {
    padding: 15px;
  }

  .report-table th,
  .report-table td {
    padding: 8px 4px;
    font-size: 10px;
  }

  /* تنسيقات DataTables للشاشات الصغيرة */
  .dataTables_wrapper .dataTables_length,
  .dataTables_wrapper .dataTables_filter,
  .dataTables_wrapper .dataTables_info,
  .dataTables_wrapper .dataTables_paginate {
    text-align: center;
    margin-bottom: 10px;
  }

  .dataTables_wrapper .dataTables_filter input {
    width: 100%;
    max-width: 200px;
  }

  #addedVacationsTable thead th,
  #addedVacationsTable tbody td {
    padding: 6px 4px;
    font-size: 11px;
  }

  #addedVacationsTable .edit-added-btn,
  #addedVacationsTable .delete-added-btn {
    padding: 4px 8px;
    font-size: 10px;
    margin: 1px;
  }

  .dt-button {
    padding: 6px 10px !important;
    font-size: 12px !important;
  }

  /* تنسيقات الفلاتر المتقدمة للشاشات الصغيرة */
  .advanced-search-container {
    padding: 15px;
  }

  .search-filters-row {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .filter-actions {
    flex-direction: column;
    gap: 10px;
  }

  .search-btn, .reset-btn {
    width: 100%;
    padding: 12px;
  }
}

/* تنسيقات DataTables المخصصة */
.dataTables_wrapper {
  direction: rtl;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_paginate {
  direction: rtl;
  text-align: right;
}

.dataTables_wrapper .dataTables_filter input {
  margin-right: 0.5em;
  margin-left: 0;
  direction: rtl;
  text-align: right;
}

.dataTables_wrapper .dataTables_length select {
  margin-right: 0.5em;
  margin-left: 0;
}

/* تنسيق الجدول */
#addedVacationsTable {
  width: 100% !important;
  border-collapse: collapse;
  margin-top: 1em;
}

#addedVacationsTable thead th {
  background-color: #2c3e50;
  color: white;
  font-weight: bold;
  text-align: center;
  padding: 12px 8px;
  border: 1px solid #34495e;
}

#addedVacationsTable tbody td {
  padding: 10px 8px;
  text-align: center;
  border: 1px solid #ddd;
  vertical-align: middle;
}

#addedVacationsTable tbody tr:nth-child(even) {
  background-color: #f8f9fa;
}

#addedVacationsTable tbody tr:hover {
  background-color: #e3f2fd;
}

/* تنسيق أزرار الإجراءات */
#addedVacationsTable .edit-added-btn,
#addedVacationsTable .delete-added-btn {
  padding: 6px 12px;
  margin: 2px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: bold;
}

#addedVacationsTable .edit-added-btn {
  background-color: #3498db;
  color: white;
}

#addedVacationsTable .edit-added-btn:hover {
  background-color: #2980b9;
}

#addedVacationsTable .delete-added-btn {
  background-color: #e74c3c;
  color: white;
}

#addedVacationsTable .delete-added-btn:hover {
  background-color: #c0392b;
}

/* تنسيق أزرار DataTables */
.dt-buttons {
  margin-bottom: 1em;
  direction: rtl;
}

.dt-button {
  background-color: #27ae60 !important;
  color: white !important;
  border: none !important;
  padding: 8px 16px !important;
  border-radius: 4px !important;
  margin-left: 5px !important;
  font-weight: bold !important;
}

.dt-button:hover {
  background-color: #229954 !important;
}

/* تنسيق التصفح */
.dataTables_paginate .paginate_button {
  padding: 6px 12px !important;
  margin-left: 2px !important;
  border: 1px solid #ddd !important;
  border-radius: 4px !important;
}

.dataTables_paginate .paginate_button.current {
  background-color: #3498db !important;
  color: white !important;
  border-color: #3498db !important;
}

.dataTables_paginate .paginate_button:hover {
  background-color: #ecf0f1 !important;
  border-color: #bdc3c7 !important;
}

/* تنسيق البحث */
.dataTables_filter label {
  font-weight: bold;
  color: #2c3e50;
}

.dataTables_filter input {
  border: 2px solid #bdc3c7;
  border-radius: 4px;
  padding: 6px 10px;
  font-size: 14px;
}

.dataTables_filter input:focus {
  border-color: #3498db;
  outline: none;
  box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
}

/* تنسيق معلومات الجدول */
.dataTables_info {
  font-weight: bold;
  color: #2c3e50;
  margin-top: 1em;
}

/* تنسيق رسالة التحميل */
.dataTables_processing {
  background-color: rgba(255, 255, 255, 0.9) !important;
  border: 2px solid #3498db !important;
  border-radius: 8px !important;
  color: #2c3e50 !important;
  font-weight: bold !important;
  font-size: 16px !important;
}

/* تنسيق فلاتر البحث المتقدمة */
.advanced-search-container {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.search-filters-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 15px;
}

.filter-actions {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.filter-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  direction: rtl;
  text-align: right;
}

.filter-input:focus {
  border-color: #3498db;
  outline: none;
  box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
}

.search-btn, .reset-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  font-size: 14px;
}

.search-btn {
  background-color: #3498db;
  color: white;
}

.search-btn:hover {
  background-color: #2980b9;
}

.reset-btn {
  background-color: #95a5a6;
  color: white;
}

.reset-btn:hover {
  background-color: #7f8c8d;
}

.filter-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  direction: rtl;
  text-align: right;
}

.filter-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.filter-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-start;
  flex-wrap: wrap;
}

.filter-actions button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.search-btn {
  background-color: #007bff;
  color: white;
}

.search-btn:hover {
  background-color: #0056b3;
}

.reset-btn {
  background-color: #6c757d;
  color: white;
}

.reset-btn:hover {
  background-color: #545b62;
}

/* تنسيق شاشة تاريخ نهاية الإجازة */
#vacationEndDateInfo {
  background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%) !important;
  border: 1px solid #c8e6c9 !important;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1) !important;
  transition: all 0.3s ease !important;
}

#vacationEndDateInfo:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.15) !important;
}

#calculatedEndDate {
  background-color: rgba(255, 255, 255, 0.8) !important;
  padding: 8px 16px !important;
  border-radius: 6px !important;
  display: inline-block !important;
  margin-top: 5px !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

/* تنسيقات متجاوبة لشاشة تاريخ نهاية الإجازة */
@media (max-width: 768px) {
  #calculatedEndDate {
    font-size: 1.2em !important;
  }
}

/* تنسيق عناصر التحكم في الصفحات */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-top: 20px;
  border: 1px solid #e9ecef;
  flex-wrap: wrap;
  gap: 15px;
}

.pagination-info {
  font-size: 14px;
  color: #495057;
  font-weight: 500;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-btn {
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  background: white;
  color: #495057;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 5px;
}

.pagination-btn:hover:not(:disabled) {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.pagination-btn:disabled {
  background: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

.page-numbers {
  display: flex;
  gap: 4px;
  margin: 0 10px;
}

.page-number {
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  background: white;
  color: #495057;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  min-width: 40px;
  text-align: center;
}

.page-number:hover {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.page-number.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
  font-weight: bold;
}

.pagination-size {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #495057;
}

.pagination-size select {
  padding: 6px 10px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  background: white;
}

@media (max-width: 768px) {
  .pagination-container {
    flex-direction: column;
    text-align: center;
  }

  .pagination-controls {
    order: 2;
  }

  .pagination-info {
    order: 1;
  }

  .pagination-size {
    order: 3;
  }
}