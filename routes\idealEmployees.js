const express = require('express');
const router = express.Router();
const { logAction, createEditMessage } = require('../activityLogger');
const { enhanceSearchQuery } = require('../utils/textUtils');
const { addDepartmentFilter } = require('../middleware/departmentFilter');

/**
 * دالة لإضافة فلترة الإدارات لاستعلامات العمال المثاليين
 * @param {string} whereClause - شرط WHERE الحالي
 * @param {Array} queryParams - معاملات الاستعلام الحالية
 * @param {Object} req - كائن الطلب
 * @returns {Object} - كائن يحتوي على whereClause و queryParams المحدثة
 */
function addDepartmentFilterToIdealEmployees(whereClause, queryParams, req) {
  console.log(`🔍 Ideal Employees Filter - allowedDepartments:`, req.allowedDepartments);

  if (req.allowedDepartments === null) {
    // المستخدم admin - لا نطبق فلترة
    console.log(`👑 Admin user - no filtering applied to ideal employees`);
    return { whereClause, queryParams };
  } else if (req.allowedDepartments && req.allowedDepartments.length > 0) {
    // تطبيق فلترة الإدارات المسموحة - فلترة على الإدارة الحالية للموظف
    const departmentPlaceholders = req.allowedDepartments.map(() => '?').join(',');
    whereClause += ` AND e.department IN (${departmentPlaceholders}) AND e.department IS NOT NULL`;
    queryParams.push(...req.allowedDepartments);
    console.log(`✅ Ideal Employees filter applied for departments:`, req.allowedDepartments);
    console.log(`📝 Final WHERE clause:`, whereClause);
    return { whereClause, queryParams };
  } else {
    // لا يوجد إدارات مسموحة - لا يعرض أي نتائج
    whereClause += ` AND 1 = 0`;
    console.log(`🚫 No departments allowed - blocking all ideal employees data`);
    console.log(`📝 Final WHERE clause:`, whereClause);
    return { whereClause, queryParams };
  }
}

// دالة للتحقق من صحة التوكن
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'غير مصرح' });
  }

  const jwt = require('jsonwebtoken');
const { cleanUpdateData, cleanInsertData, prepareUpdateQuery, prepareInsertQuery } = require('../utils/dataCleanup');
  jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key', (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'توكن غير صالح' });
    }
    req.user = user;
    next();
  });
};

// متغير لتتبع ما إذا تم إنشاء الجدول أم لا
let tableCreated = false;

// إنشاء جدول العمال المثاليين إذا لم يكن موجوداً
const createIdealEmployeesTable = async (pool) => {
  if (tableCreated) return; // إذا تم إنشاء الجدول بالفعل، لا تفعل شيئاً

  try {
    await pool.promise().query(`
      CREATE TABLE IF NOT EXISTS ideal_employees (
        id int NOT NULL AUTO_INCREMENT,
        employee_code varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
        employee_name varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
        department varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
        from_period date NOT NULL,
        to_period date NOT NULL,
        evaluation_score decimal(5,2) NOT NULL,
        reward_amount decimal(10,2) NOT NULL,
        selection_reason text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
        notes text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
        created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY idx_employee_code (employee_code),
        KEY idx_department (department),
        KEY idx_period (from_period,to_period),
        KEY idx_evaluation_score (evaluation_score),
        KEY idx_created_at (created_at)
      ) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول العامل المثالي'
    `);
    tableCreated = true; // تعيين المتغير إلى true بعد إنشاء الجدول
  } catch (error) {
    console.error('خطأ في إنشاء جدول العمال المثاليين:', error);
    throw error;
  }
};

// اختبار API للعامل المثالي
router.get('/test', (req, res) => {
  res.json({ message: 'API العامل المثالي يعمل بنجاح', timestamp: new Date().toISOString() });
});

// الحصول على جميع العمال المثاليين
router.get('/', authenticateToken, addDepartmentFilter, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    await createIdealEmployeesTable(pool);

    const [rows] = await pool.promise().execute(
      'SELECT * FROM ideal_employees ORDER BY created_at DESC, id DESC'
    );
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب العمال المثاليين:', error);
    res.status(500).json({ message: 'خطأ في جلب البيانات' });
  }
});

// إضافة عامل مثالي جديد
router.post('/', authenticateToken, async (req, res) => {
  const {
    employee_code,
    employee_name,
    department,
    from_period,
    to_period,
    evaluation_score,
    reward_amount,
    selection_reason,
    notes
  } = req.body;



  try {
    const pool = req.app.locals.pool;
    await createIdealEmployeesTable(pool);

    // معالجة التواريخ - تحويل إلى صيغة YYYY-MM-DD
    let processedFromPeriod = from_period;
    let processedToPeriod = to_period;

    if (from_period) {
      const fromDate = new Date(from_period);
      if (!isNaN(fromDate.getTime())) {
        processedFromPeriod = fromDate.toISOString().split('T')[0];
      }
    }

    if (to_period) {
      const toDate = new Date(to_period);
      if (!isNaN(toDate.getTime())) {
        processedToPeriod = toDate.toISOString().split('T')[0];
      }
    }

    // التحقق من البيانات المطلوبة
    if (!employee_code || !employee_name || !department || !processedFromPeriod || !processedToPeriod ||
        evaluation_score === undefined || evaluation_score === null || evaluation_score === '' ||
        reward_amount === undefined || reward_amount === null || reward_amount === '' ||
        !selection_reason) {
      return res.status(400).json({ message: 'جميع الحقول مطلوبة' });
    }

    // التحقق من صحة البيانات الرقمية
    if (isNaN(evaluation_score) || parseFloat(evaluation_score) < 0 || parseFloat(evaluation_score) > 100) {
      return res.status(400).json({ message: 'درجة التقييم يجب أن تكون رقماً بين 0 و 100' });
    }

    if (isNaN(reward_amount) || parseFloat(reward_amount) < 0) {
      return res.status(400).json({ message: 'مبلغ المكافأة يجب أن يكون رقماً موجباً' });
    }

    // التحقق من صحة التواريخ
    if (!processedFromPeriod || processedFromPeriod === 'Invalid Date') {
      return res.status(400).json({ message: 'تاريخ بداية الفترة غير صحيح' });
    }

    if (!processedToPeriod || processedToPeriod === 'Invalid Date') {
      return res.status(400).json({ message: 'تاريخ نهاية الفترة غير صحيح' });
    }

    // التحقق من أن تاريخ البداية أقل من تاريخ النهاية
    if (new Date(processedFromPeriod) >= new Date(processedToPeriod)) {
      return res.status(400).json({ message: 'تاريخ بداية الفترة يجب أن يكون قبل تاريخ النهاية' });
    }

    // التحقق من وجود الموظف في جدول الموظفين
    const [employeeCheck] = await pool.promise().execute(
      "SELECT code, full_name, department, status FROM employees WHERE code = ?",
      [employee_code]
    );

    if (employeeCheck.length === 0) {
      return res.status(400).json({ message: 'الموظف غير موجود في النظام' });
    }

    if (employeeCheck[0].status === 'مستقيل') {
      return res.status(400).json({ message: 'لا يمكن إضافة عامل مثالي لموظف مستقيل' });
    }

    // التحقق من عدم وجود تداخل في الفترات لنفس الموظف
    const [existingRecords] = await pool.promise().execute(
      `SELECT id FROM ideal_employees
       WHERE employee_code = ?
       AND ((from_period <= ? AND to_period >= ?)
            OR (from_period <= ? AND to_period >= ?)
            OR (from_period >= ? AND to_period <= ?))`,
      [employee_code, processedToPeriod, processedFromPeriod, processedFromPeriod, processedToPeriod, processedFromPeriod, processedToPeriod]
    );

    if (existingRecords.length > 0) {
      return res.status(400).json({ 
        message: 'يوجد تداخل في الفترات الزمنية لهذا الموظف. يرجى اختيار فترة مختلفة.' 
      });
    }

    const [result] = await pool.promise().execute(
      `INSERT INTO ideal_employees
       (employee_code, employee_name, department, from_period, to_period, evaluation_score, reward_amount, selection_reason, notes)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [employee_code, employee_name, department, processedFromPeriod, processedToPeriod, evaluation_score, reward_amount, selection_reason, notes]
    );



    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'add',
      module: 'ideal_employees',
      record_id: result.insertId.toString(),
      message: `تم إضافة عامل مثالي: ${employee_name} (كود: ${employee_code}) - القسم: ${department} - الفترة: من ${processedFromPeriod} إلى ${processedToPeriod} - الدرجة: ${evaluation_score} - المكافأة: ${reward_amount}`
    });

    res.status(201).json({ 
      message: 'تم إضافة العامل المثالي بنجاح',
      id: result.insertId
    });
  } catch (error) {
    console.error('خطأ في إضافة العامل المثالي:', error);
    res.status(500).json({ message: 'خطأ في إضافة البيانات' });
  }
});

// تحديث عامل مثالي
router.put('/:id', authenticateToken, async (req, res) => {
  const { id } = req.params;
  const {
    employee_code,
    employee_name,
    department,
    from_period,
    to_period,
    evaluation_score,
    reward_amount,
    selection_reason,
    notes
  } = req.body;

  try {
    const pool = req.app.locals.pool;
    await createIdealEmployeesTable(pool);

    // معالجة التواريخ - تحويل إلى صيغة YYYY-MM-DD
    let processedFromPeriod = from_period;
    let processedToPeriod = to_period;

    if (from_period) {
      const fromDate = new Date(from_period);
      if (!isNaN(fromDate.getTime())) {
        processedFromPeriod = fromDate.toISOString().split('T')[0];
      }
    }

    if (to_period) {
      const toDate = new Date(to_period);
      if (!isNaN(toDate.getTime())) {
        processedToPeriod = toDate.toISOString().split('T')[0];
      }
    }

    // التحقق من البيانات المطلوبة
    const missingFields = [];

    if (!employee_code || employee_code.trim() === '') missingFields.push('كود الموظف');
    if (!employee_name || employee_name.trim() === '') missingFields.push('اسم الموظف');
    if (!department || department.trim() === '') missingFields.push('القسم');
    if (!processedFromPeriod) missingFields.push('تاريخ بداية الفترة');
    if (!processedToPeriod) missingFields.push('تاريخ نهاية الفترة');
    if (evaluation_score === undefined || evaluation_score === null || evaluation_score === '') missingFields.push('درجة التقييم');
    if (reward_amount === undefined || reward_amount === null || reward_amount === '') missingFields.push('مبلغ المكافأة');
    if (!selection_reason || selection_reason.trim() === '') missingFields.push('سبب الاختيار');

    if (missingFields.length > 0) {
      return res.status(400).json({
        message: `الحقول التالية مطلوبة: ${missingFields.join(', ')}`,
        missingFields
      });
    }

    // التحقق من صحة البيانات الرقمية
    if (isNaN(evaluation_score) || parseFloat(evaluation_score) < 0 || parseFloat(evaluation_score) > 100) {
      return res.status(400).json({ message: 'درجة التقييم يجب أن تكون رقماً بين 0 و 100' });
    }

    if (isNaN(reward_amount) || parseFloat(reward_amount) < 0) {
      return res.status(400).json({ message: 'مبلغ المكافأة يجب أن يكون رقماً موجباً' });
    }

    // التحقق من صحة التواريخ
    if (!processedFromPeriod || processedFromPeriod === 'Invalid Date') {
      return res.status(400).json({ message: 'تاريخ بداية الفترة غير صحيح' });
    }

    if (!processedToPeriod || processedToPeriod === 'Invalid Date') {
      return res.status(400).json({ message: 'تاريخ نهاية الفترة غير صحيح' });
    }

    // التحقق من أن تاريخ البداية أقل من تاريخ النهاية
    if (new Date(processedFromPeriod) >= new Date(processedToPeriod)) {
      return res.status(400).json({ message: 'تاريخ بداية الفترة يجب أن يكون قبل تاريخ النهاية' });
    }

    // التحقق من وجود الموظف في جدول الموظفين
    const [employeeCheck] = await pool.promise().execute(
      "SELECT code, full_name, department, status FROM employees WHERE code = ?",
      [employee_code]
    );

    if (employeeCheck.length === 0) {
      return res.status(400).json({ message: 'الموظف غير موجود في النظام' });
    }

    if (employeeCheck[0].status === 'مستقيل') {
      return res.status(400).json({ message: 'لا يمكن تحديث عامل مثالي لموظف مستقيل' });
    }

    // الحصول على البيانات القديمة للمقارنة
    const [oldDataResult] = await pool.promise().execute(
      'SELECT * FROM ideal_employees WHERE id = ?',
      [id]
    );

    if (oldDataResult.length === 0) {
      return res.status(404).json({ message: 'العامل المثالي غير موجود' });
    }

    const oldData = oldDataResult[0];

    // التحقق من عدم وجود تداخل في الفترات لنفس الموظف (باستثناء السجل الحالي)
    const [existingRecords] = await pool.promise().execute(
      `SELECT id FROM ideal_employees
       WHERE employee_code = ? AND id != ?
       AND ((from_period <= ? AND to_period >= ?)
            OR (from_period <= ? AND to_period >= ?)
            OR (from_period >= ? AND to_period <= ?))`,
      [employee_code, id, processedToPeriod, processedFromPeriod, processedFromPeriod, processedToPeriod, processedFromPeriod, processedToPeriod]
    );

    if (existingRecords.length > 0) {
      return res.status(400).json({
        message: 'يوجد تداخل في الفترات الزمنية لهذا الموظف. يرجى اختيار فترة مختلفة.'
      });
    }

    const [result] = await pool.promise().execute(
      `UPDATE ideal_employees SET
       employee_code = ?, employee_name = ?, department = ?, from_period = ?, to_period = ?,
       evaluation_score = ?, reward_amount = ?, selection_reason = ?, notes = ?
       WHERE id = ?`,
      [employee_code, employee_name, department, processedFromPeriod, processedToPeriod, evaluation_score, reward_amount, selection_reason, notes, id]
    );

    // إنشاء رسالة التغييرات التفصيلية
    const fieldLabels = {
      employee_code: 'كود الموظف',
      employee_name: 'اسم الموظف',
      department: 'القسم',
      from_period: 'من تاريخ',
      to_period: 'إلى تاريخ',
      evaluation_score: 'درجة التقييم',
      reward_amount: 'مبلغ المكافأة',
      selection_reason: 'سبب الاختيار',
      notes: 'الملاحظات'
    };

    const newData = { from_period: processedFromPeriod, to_period: processedToPeriod, evaluation_score, reward_amount, selection_reason, notes };
    const editMessage = createEditMessage('عامل مثالي', oldData, newData, fieldLabels);

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'edit',
      module: 'ideal_employees',
      record_id: id.toString(),
      message: `تم تعديل عامل مثالي: ${oldData.employee_name} (كود: ${oldData.employee_code}) - ${editMessage}`
    });

    res.json({ message: 'تم تحديث العامل المثالي بنجاح' });
  } catch (error) {
    console.error('خطأ في تحديث العامل المثالي:', error);
    res.status(500).json({ message: 'خطأ في تحديث البيانات' });
  }
});

// حذف عامل مثالي
router.delete('/:id', authenticateToken, async (req, res) => {
  const { id } = req.params;

  try {
    const pool = req.app.locals.pool;
    await createIdealEmployeesTable(pool);

    // الحصول على بيانات العامل المثالي قبل الحذف
    const [idealEmployeeData] = await pool.promise().execute(
      'SELECT employee_code, employee_name, department, from_period, to_period, evaluation_score, reward_amount FROM ideal_employees WHERE id = ?',
      [id]
    );

    if (idealEmployeeData.length === 0) {
      return res.status(404).json({ message: 'العامل المثالي غير موجود' });
    }

    const idealEmployee = idealEmployeeData[0];

    const [result] = await pool.promise().execute(
      'DELETE FROM ideal_employees WHERE id = ?',
      [id]
    );

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'delete',
      module: 'ideal_employees',
      record_id: id.toString(),
      message: `تم حذف عامل مثالي: ${idealEmployee.employee_name} (كود: ${idealEmployee.employee_code}) - القسم: ${idealEmployee.department} - الفترة: من ${idealEmployee.from_period} إلى ${idealEmployee.to_period} - الدرجة: ${idealEmployee.evaluation_score} - المكافأة: ${idealEmployee.reward_amount}`
    });

    res.json({ message: 'تم حذف العامل المثالي بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف العامل المثالي:', error);
    res.status(500).json({ message: 'خطأ في حذف البيانات' });
  }
});

// DataTables server-side processing للعمال المثاليين
router.get('/datatables', authenticateToken, addDepartmentFilter, async (req, res) => {
  console.log('🔍 تم استقبال طلب DataTables للعمال المثاليين');
  console.log('📥 Query parameters:', req.query);

  // استخراج معاملات البحث المتقدم
  const advancedSearchParams = {
    employeeCode: req.query.employeeCode || '',
    employeeName: req.query.employeeName || '',
    department: req.query.department || '',
    selectionReason: req.query.selectionReason || '',
    minScore: req.query.minScore || '',
    maxScore: req.query.maxScore || '',
    minReward: req.query.minReward || '',
    maxReward: req.query.maxReward || '',
    dateFrom: req.query.dateFrom || '',
    dateTo: req.query.dateTo || ''
  };

  console.log('🔍 معاملات البحث المستخرجة:', advancedSearchParams);

  try {
    const pool = req.app.locals.pool;
    await createIdealEmployeesTable(pool);

    // معاملات DataTables
    const draw = parseInt(req.query.draw) || 1;
    const start = parseInt(req.query.start) || 0;
    const length = parseInt(req.query.length) || 10;
    const searchValue = req.query.search?.value || '';
    const orderColumnIndex = parseInt(req.query.order?.[0]?.column) || 0;
    const orderDirection = req.query.order?.[0]?.dir || 'desc';

    // أسماء الأعمدة للترتيب
    const columns = [
      'employee_code',
      'employee_name',
      'department',
      'from_period',
      'to_period',
      'evaluation_score',
      'reward_amount',
      'selection_reason'
    ];

    const orderColumn = columns[orderColumnIndex] || 'created_at';

    // بناء الاستعلام الأساسي
    let baseQuery = `
      SELECT
        id,
        employee_code,
        employee_name,
        department,
        DATE_FORMAT(from_period, '%Y-%m-%d') as from_period_formatted,
        DATE_FORMAT(to_period, '%Y-%m-%d') as to_period_formatted,
        evaluation_score,
        reward_amount,
        selection_reason,
        notes,
        DATE_FORMAT(created_at, '%Y-%m-%d %H:%i:%s') as created_at_formatted
      FROM ideal_employees
      WHERE 1=1
    `;

    let countQuery = `
      SELECT COUNT(*) as total
      FROM ideal_employees
      WHERE 1=1
    `;

    const params = [];
    const countParams = [];

    // إضافة فلترة الإدارات أولاً
    if (req.allowedDepartments === null) {
      // المستخدم admin - لا نطبق فلترة إضافية
      console.log(`👑 Admin user - no department filtering applied to ideal employees DataTables`);
    } else if (req.allowedDepartments && req.allowedDepartments.length > 0) {
      // تطبيق فلترة الإدارات المسموحة
      const departmentPlaceholders = req.allowedDepartments.map(() => '?').join(',');
      const departmentCondition = ` AND department IN (${departmentPlaceholders}) AND department IS NOT NULL`;
      baseQuery += departmentCondition;
      countQuery += departmentCondition;

      req.allowedDepartments.forEach(dept => {
        params.push(dept);
        countParams.push(dept);
      });

      console.log(`✅ Ideal employees DataTables filter applied for departments:`, req.allowedDepartments);
    } else {
      // لا يوجد إدارات مسموحة - لا يعرض أي نتائج
      const blockCondition = ` AND 1 = 0`;
      baseQuery += blockCondition;
      countQuery += blockCondition;
      console.log(`🚫 No departments allowed - blocking all ideal employees data in DataTables`);
    }

    // إضافة شروط البحث المتقدم
    if (advancedSearchParams.employeeCode) {
      const condition = " AND employee_code LIKE ?";
      baseQuery += condition;
      countQuery += condition;
      const param = `%${advancedSearchParams.employeeCode}%`;
      params.push(param);
      countParams.push(param);
    }

    if (advancedSearchParams.employeeName) {
      const condition = " AND employee_name LIKE ?";
      baseQuery += condition;
      countQuery += condition;
      const param = `%${advancedSearchParams.employeeName}%`;
      params.push(param);
      countParams.push(param);
    }

    if (advancedSearchParams.department) {
      const condition = " AND department LIKE ?";
      baseQuery += condition;
      countQuery += condition;
      const param = `%${advancedSearchParams.department}%`;
      params.push(param);
      countParams.push(param);
    }

    if (advancedSearchParams.selectionReason) {
      const condition = " AND selection_reason LIKE ?";
      baseQuery += condition;
      countQuery += condition;
      const param = `%${advancedSearchParams.selectionReason}%`;
      params.push(param);
      countParams.push(param);
    }

    if (advancedSearchParams.minScore) {
      const condition = " AND evaluation_score >= ?";
      baseQuery += condition;
      countQuery += condition;
      const param = parseFloat(advancedSearchParams.minScore);
      params.push(param);
      countParams.push(param);
    }

    if (advancedSearchParams.maxScore) {
      const condition = " AND evaluation_score <= ?";
      baseQuery += condition;
      countQuery += condition;
      const param = parseFloat(advancedSearchParams.maxScore);
      params.push(param);
      countParams.push(param);
    }

    if (advancedSearchParams.minReward) {
      const condition = " AND reward_amount >= ?";
      baseQuery += condition;
      countQuery += condition;
      const param = parseFloat(advancedSearchParams.minReward);
      params.push(param);
      countParams.push(param);
    }

    if (advancedSearchParams.maxReward) {
      const condition = " AND reward_amount <= ?";
      baseQuery += condition;
      countQuery += condition;
      const param = parseFloat(advancedSearchParams.maxReward);
      params.push(param);
      countParams.push(param);
    }

    if (advancedSearchParams.dateFrom) {
      const condition = " AND from_period >= ?";
      baseQuery += condition;
      countQuery += condition;
      const param = advancedSearchParams.dateFrom;
      params.push(param);
      countParams.push(param);
    }

    if (advancedSearchParams.dateTo) {
      const condition = " AND to_period <= ?";
      baseQuery += condition;
      countQuery += condition;
      const param = advancedSearchParams.dateTo;
      params.push(param);
      countParams.push(param);
    }

    // إضافة البحث العام
    if (searchValue) {
      const searchCondition = ` AND (
        employee_code LIKE ? OR
        employee_name LIKE ? OR
        department LIKE ? OR
        selection_reason LIKE ?
      )`;
      baseQuery += searchCondition;
      countQuery += searchCondition;

      const searchParam = `%${searchValue}%`;
      params.push(searchParam, searchParam, searchParam, searchParam);
      countParams.push(searchParam, searchParam, searchParam, searchParam);
    }

    // الحصول على العدد الإجمالي
    const [countResult] = await pool.promise().query(countQuery, countParams);
    const totalRecords = countResult[0].total;

    // إضافة الترتيب والتصفح
    baseQuery += ` ORDER BY ${orderColumn} ${orderDirection.toUpperCase()}`;
    baseQuery += ` LIMIT ${length} OFFSET ${start}`;

    console.log('🔍 Final query:', baseQuery);
    console.log('🔍 Query params:', params);

    // تنفيذ الاستعلام
    const [rows] = await pool.promise().query(baseQuery, params);

    // تنسيق البيانات لـ DataTables
    const formattedData = rows.map(row => {
      return [
        row.employee_code,
        row.employee_name,
        row.department,
        row.from_period_formatted,
        row.to_period_formatted,
        parseFloat(row.evaluation_score) % 1 === 0 ? parseInt(row.evaluation_score) : parseFloat(row.evaluation_score).toFixed(1),
        parseFloat(row.reward_amount) % 1 === 0 ? parseInt(row.reward_amount) : parseFloat(row.reward_amount).toFixed(2),
        row.selection_reason,
        `<button class="edit-btn" data-ideal-id="${row.id}" data-permission="can_edit">تعديل</button>
         <button class="delete-btn" data-ideal-id="${row.id}" data-permission="can_delete">حذف</button>`
      ];
    });

    const response = {
      draw: draw,
      recordsTotal: totalRecords,
      recordsFiltered: totalRecords,
      data: formattedData
    };

    console.log('📤 إرسال الاستجابة:', {
      draw: response.draw,
      recordsTotal: response.recordsTotal,
      recordsFiltered: response.recordsFiltered,
      dataCount: response.data.length
    });

    res.json(response);

  } catch (error) {
    console.error('❌ خطأ في DataTables للعمال المثاليين:', error);
    res.status(500).json({
      draw: parseInt(req.query.draw) || 1,
      recordsTotal: 0,
      recordsFiltered: 0,
      data: [],
      error: 'فشل في جلب البيانات'
    });
  }
});

// البحث في العمال المثاليين
router.get('/search', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    await createIdealEmployeesTable(pool);

    const { employee_code, employee_name, department, start_date, end_date, min_score, max_score } = req.query;

    let query = "SELECT * FROM ideal_employees WHERE 1=1";
    const params = [];

    if (employee_code) {
      query += " AND employee_code = ?";
      params.push(employee_code);
    }

    if (employee_name) {
      const searchColumns = ['employee_name'];
      const enhanced = enhanceSearchQuery('SELECT 1', employee_name, searchColumns, []);
      const searchCondition = enhanced.query.replace('SELECT 1 WHERE ', '');
      query += ` AND ${searchCondition}`;
      params.push(...enhanced.params);
    }

    if (department) {
      query += " AND department = ?";
      params.push(department);
    }

    if (start_date) {
      query += " AND from_period >= ?";
      params.push(start_date);
    }

    if (end_date) {
      query += " AND to_period <= ?";
      params.push(end_date);
    }

    if (min_score) {
      query += " AND evaluation_score >= ?";
      params.push(parseFloat(min_score));
    }

    if (max_score) {
      query += " AND evaluation_score <= ?";
      params.push(parseFloat(max_score));
    }

    query += " ORDER BY from_period DESC, evaluation_score DESC";

    const [rows] = await pool.promise().query(query, params);
    res.json(rows);
  } catch (error) {
    console.error('خطأ في البحث عن العمال المثاليين:', error);
    res.status(500).json({ error: 'فشل في البحث عن العمال المثاليين' });
  }
});

// إحصائيات العمال المثاليين
router.get('/statistics', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    await createIdealEmployeesTable(pool);

    const { department, start_date, end_date } = req.query;

    let query = `SELECT
      COUNT(*) as total_count,
      AVG(evaluation_score) as avg_score,
      MAX(evaluation_score) as max_score,
      MIN(evaluation_score) as min_score,
      SUM(reward_amount) as total_rewards,
      AVG(reward_amount) as avg_reward
      FROM ideal_employees WHERE 1=1`;
    const params = [];

    if (department) {
      query += " AND department = ?";
      params.push(department);
    }

    if (start_date) {
      query += " AND from_period >= ?";
      params.push(start_date);
    }

    if (end_date) {
      query += " AND to_period <= ?";
      params.push(end_date);
    }

    const [stats] = await pool.promise().query(query, params);

    // إحصائيات حسب القسم
    let departmentQuery = `SELECT
      department,
      COUNT(*) as count,
      AVG(evaluation_score) as avg_score,
      SUM(reward_amount) as total_rewards
      FROM ideal_employees WHERE 1=1`;
    const departmentParams = [];

    if (start_date) {
      departmentQuery += " AND from_period >= ?";
      departmentParams.push(start_date);
    }

    if (end_date) {
      departmentQuery += " AND to_period <= ?";
      departmentParams.push(end_date);
    }

    departmentQuery += " GROUP BY department ORDER BY count DESC";

    const [departmentStats] = await pool.promise().query(departmentQuery, departmentParams);

    res.json({
      overall: stats[0],
      by_department: departmentStats
    });
  } catch (error) {
    console.error('خطأ في جلب إحصائيات العمال المثاليين:', error);
    res.status(500).json({ error: 'فشل في جلب الإحصائيات' });
  }
});

// الحصول على العمال المثاليين حسب الموظف
router.get('/employee/:code', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const { code } = req.params;

    const [rows] = await pool.promise().query(`
      SELECT * FROM ideal_employees
      WHERE employee_code = ?
      ORDER BY from_period DESC
    `, [code]);

    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب العمال المثاليين للموظف:', error);
    res.status(500).json({ error: 'فشل في جلب بيانات الموظف' });
  }
});

// الحصول على العمال المثاليين حسب القسم
router.get('/department/:department', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const { department } = req.params;

    const [rows] = await pool.promise().query(`
      SELECT * FROM ideal_employees
      WHERE department = ?
      ORDER BY from_period DESC, evaluation_score DESC
    `, [department]);

    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب العمال المثاليين للقسم:', error);
    res.status(500).json({ error: 'فشل في جلب بيانات القسم' });
  }
});

module.exports = router;
