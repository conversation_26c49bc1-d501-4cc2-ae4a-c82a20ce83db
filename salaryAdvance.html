<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>قسم السلف</title>
  <link rel="stylesheet" href="style.css" />
  <link rel="stylesheet" href="salaryAdvance.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="no-sidebar-layout.css">

  <!-- DataTables CSS -->
  <link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
  <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css">
  <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap5.min.css">

  <!-- تنسيقات خاصة بجدول السلف -->
  <link rel="stylesheet" href="training-datatables.css">

  <script src="permissions.js" defer></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

  <!-- jQuery و DataTables JavaScript -->
  <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
  <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
  <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
  <script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
  <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
  <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
  <script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
  <script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap5.min.js"></script>
</head>
<body>

    <!-- زر العودة للوحة التحكم -->
  <div class="back-to-dashboard">
    <a href="salary-advance-cards.html" class="dashboard-btn">
      <i class="fas fa-arrow-right"></i>
      <span>العودة لإدارة السلف</span>
    </a>
  </div>


<div class="main-content full-width" id="mainContent">
  <h1>قسم السلف</h1>



  <!-- محتوى إضافة سلفة جديدة -->
  <div class="tab-content" id="add-advance" style="display: none;">
    <div class="advance-form">
      <h2>إضافة سلفة جديدة</h2>
      
      <!-- البحث عن الموظف -->
      <div class="form-row">
        <div class="form-group">
          <label for="employeeSearchAdd">البحث عن الموظف (بالاسم أو الكود):</label>
          <input type="text" id="employeeSearchAdd" class="employee-search-input" placeholder="أدخل اسم أو كود الموظف" list="employeeSearchSuggestions" autocomplete="off">
          <datalist id="employeeSearchSuggestions"></datalist>
        </div>
      </div>

      <!-- الصف الأول: كود الموظف، اسم الموظف، الإدارة -->
      <div class="form-row">
        <div class="form-group">
          <label for="employeeCode">كود الموظف:</label>
          <input type="text" id="employeeCode" placeholder="كود الموظف" readonly>
        </div>

        <div class="form-group">
          <label for="employeeName">اسم الموظف:</label>
          <input type="text" id="employeeName" placeholder="اسم الموظف" readonly>
        </div>

        <div class="form-group">
          <label for="employeeDepartment">الإدارة:</label>
          <input type="text" id="employeeDepartment" placeholder="الإدارة" readonly>
        </div>
      </div>

      <!-- الصف الثاني: قيمة السلفة، تاريخ السلفة، سبب السلفة، طريقة السداد -->
      <div class="form-row">
        <div class="form-group">
          <label for="advanceAmount">قيمة السلفة:</label>
          <input type="number" id="advanceAmount" placeholder="قيمة السلفة" min="0" step="0.01" required>
        </div>

        <div class="form-group">
          <label for="advanceDate">تاريخ السلفة:</label>
          <input type="date" id="advanceDate" required>
        </div>

        <div class="form-group">
          <label for="advanceReason">سبب السلفة:</label>
          <input type="text" id="advanceReason" placeholder="سبب السلفة" required>
        </div>

        <div class="form-group">
          <label for="paymentMethod">طريقة السداد:</label>
          <input type="text" id="paymentMethod" placeholder="طريقة السداد" required>
        </div>
      </div>

      <!-- الصف الثالث: الملاحظات -->
      <div class="form-row">
        <div class="form-group full-width">
          <label for="notes">ملاحظات إضافية:</label>
          <textarea id="notes" placeholder="ملاحظات إضافية" rows="3"></textarea>
        </div>
      </div>

      <div class="form-actions">
        <button type="button" id="saveAdvance" class="btn btn-primary">
          <i class="fas fa-save"></i>
          حفظ السلفة
        </button>
        <button type="button" id="resetForm" class="btn btn-secondary">
          <i class="fas fa-undo"></i>
          إعادة تعيين
        </button>
      </div>
    </div>

    <!-- جدول السلف المضافة -->
    <div class="advances-table-container">
      <h3>السلف المسجلة</h3>

      <!-- فلاتر البحث المتقدمة -->
      <div class="advanced-search-container">
        <h4>فلاتر البحث المتقدمة</h4>

        <!-- الصف الأول من الفلاتر -->
        <div class="search-filters-row">
          <div class="filter-group">
            <label for="searchAdvanceId">رقم السجل:</label>
            <input type="text" id="searchAdvanceId" class="filter-input" placeholder="رقم السجل">
          </div>

          <div class="filter-group">
            <label for="searchAdvanceEmployeeCode">كود الموظف:</label>
            <input type="text" id="searchAdvanceEmployeeCode" class="filter-input" placeholder="كود الموظف">
          </div>

          <div class="filter-group">
            <label for="searchAdvanceEmployeeName">اسم الموظف:</label>
            <input type="text" id="searchAdvanceEmployeeName" class="filter-input" placeholder="اسم الموظف">
          </div>

          <div class="filter-group">
            <label for="searchAdvanceDepartment">الإدارة:</label>
            <select id="searchAdvanceDepartment" class="filter-input">
              <option value="">جميع الإدارات</option>
            </select>
          </div>
        </div>

        <!-- الصف الثاني من الفلاتر -->
        <div class="search-filters-row">
          <div class="filter-group">
            <label for="searchAdvanceDateFrom">من تاريخ:</label>
            <input type="date" id="searchAdvanceDateFrom" class="filter-input">
          </div>

          <div class="filter-group">
            <label for="searchAdvanceDateTo">إلى تاريخ:</label>
            <input type="date" id="searchAdvanceDateTo" class="filter-input">
          </div>
        </div>

        <!-- الصف الثالث من الفلاتر -->
        <div class="search-filters-row">
          <div class="filter-group">
            <label for="searchAdvanceMinAmount">أقل مبلغ:</label>
            <input type="number" id="searchAdvanceMinAmount" class="filter-input" placeholder="0" min="0" step="0.01">
          </div>

          <div class="filter-group">
            <label for="searchAdvanceMaxAmount">أعلى مبلغ:</label>
            <input type="number" id="searchAdvanceMaxAmount" class="filter-input" placeholder="0" min="0" step="0.01">
          </div>
        </div>

        <!-- أزرار الفلاتر -->
        <div class="filter-actions">
          <button type="button" id="applyAdvanceFilters" class="btn btn-primary">تطبيق الفلاتر</button>
          <button type="button" id="clearAdvanceFilters" class="btn btn-secondary">مسح الفلاتر</button>
        </div>
      </div>

      <div class="table-wrapper">
        <table id="addedAdvancesTable" class="added-advances-table display nowrap" style="width:100%">
          <thead>
            <tr>
              <th>رقم السجل</th>
              <th>كود الموظف</th>
              <th>اسم الموظف</th>
              <th>الإدارة</th>
              <th>قيمة السلفة</th>
              <th>تاريخ السلفة</th>
              <th>سبب السلفة</th>
              <th>طريقة السداد</th>
              <th>ملاحظات</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody></tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- محتوى تقارير السلف -->
  <div class="tab-content" id="reports">
    <div class="container-fluid">
      <div class="page-header">
        <h2><i class="fas fa-chart-bar"></i> تقارير السلف</h2>
      </div>
      
      <!-- فلاتر التقارير -->
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="card-title mb-0">
            <i class="fas fa-filter"></i>
            فلاتر التقارير
          </h5>
        </div>
        <div class="card-body">
          <div class="reports-search-filters">
            <div class="filter-row">
              <div class="filter-group">
                <label for="reportsEmployeeCode">كود الموظف:</label>
                <input type="text" id="reportsEmployeeCode" placeholder="أدخل كود الموظف">
              </div>

              <div class="filter-group">
                <label for="reportsEmployeeName">اسم الموظف:</label>
                <input type="text" id="reportsEmployeeName" placeholder="أدخل اسم الموظف">
              </div>

              <div class="filter-group">
                <label for="reportsDepartment">الإدارة:</label>
                <select id="reportsDepartment">
                  <option value="">جميع الإدارات</option>
                </select>
              </div>

              <div class="filter-group">
                <label for="reportsStartDate">من تاريخ:</label>
                <input type="date" id="reportsStartDate">
              </div>

              <div class="filter-group">
                <label for="reportsEndDate">إلى تاريخ:</label>
                <input type="date" id="reportsEndDate">
              </div>
            </div>

            <div class="filter-actions">
              <button type="button" id="applyReportsFilters" class="btn btn-primary">
                <i class="fas fa-search"></i>
                تطبيق الفلاتر
              </button>
              <button type="button" id="clearReportsFilters" class="btn btn-secondary">
                <i class="fas fa-list"></i>
                عرض الكل
              </button>
              <button type="button" id="resetReportsFilters" class="btn btn-outline">
                <i class="fas fa-undo"></i>
                إعادة تعيين
              </button>
            </div>

            <!-- عرض عدد النتائج -->
            <div id="reportsResultsInfo" class="search-results-info" style="display: none;">
              <span id="reportsResultsCount">0</span> سلفة
            </div>
          </div>
        </div>
      </div>

      <!-- إحصائيات التقرير -->
      <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
          <div class="stats-card">
            <div class="stats-card-body">
              <div class="stats-icon">
                <i class="fas fa-list-ol"></i>
              </div>
              <div class="stats-content">
                <h3 class="stats-number" id="totalAdvances">0</h3>
                <p class="stats-label">إجمالي عدد السلف</p>
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
          <div class="stats-card stats-card-success">
            <div class="stats-card-body">
              <div class="stats-icon">
                <i class="fas fa-money-bill-wave"></i>
              </div>
              <div class="stats-content">
                <h3 class="stats-number" id="totalAmount">0</h3>
                <p class="stats-label">إجمالي قيمة السلف</p>
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-6 col-md-12">
          <div class="export-buttons-container">
            <h6 class="export-title">
              <i class="fas fa-download"></i>
              تصدير التقارير
            </h6>
            <div class="export-buttons">
              <button type="button" id="printReport" class="btn btn-outline-success">
                <i class="fas fa-print"></i>
                طباعة التقرير
              </button>
              <button type="button" id="exportExcel" class="btn btn-outline-info">
                <i class="fas fa-file-excel"></i>
                تصدير Excel
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- جدول التقارير -->
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">
            <i class="fas fa-table"></i>
            نتائج التقرير
          </h5>
        </div>
        <div class="card-body p-0">
          <div class="table-responsive">
            <table id="reportsTable" class="table table-striped table-hover mb-0">
              <thead class="thead-dark">
                <tr>
                  <th>كود الموظف</th>
                  <th>اسم الموظف</th>
                  <th>الإدارة</th>
                  <th>قيمة السلفة</th>
                  <th>تاريخ السلفة</th>
                  <th>سبب السلفة</th>
                  <th>طريقة السداد</th>
                  <th>ملاحظات</th>
                </tr>
              </thead>
              <tbody id="reportsTableBody">
                <!-- سيتم ملء البيانات هنا بواسطة JavaScript -->
              </tbody>
            </table>
          </div>
        </div>

        </div>
      </div>
    </div>
  </div>
</div>

<!-- نافذة تعديل السلفة -->
<div id="editAdvanceModal" class="modal">
  <div class="modal-content">
    <div class="modal-header">
      <h3>تعديل السلفة</h3>
      <span class="close">&times;</span>
    </div>
    <div class="modal-body">
      <!-- تم حذف البحث عن الموظف من نافذة التعديل -->

      <!-- الصف الأول: كود الموظف، اسم الموظف، الإدارة -->
      <div class="form-row">
        <div class="form-group">
          <label for="editEmployeeCode">كود الموظف:</label>
          <input type="text" id="editEmployeeCode" placeholder="كود الموظف" readonly>
        </div>

        <div class="form-group">
          <label for="editEmployeeName">اسم الموظف:</label>
          <input type="text" id="editEmployeeName" placeholder="اسم الموظف" readonly>
        </div>

        <div class="form-group">
          <label for="editEmployeeDepartment">الإدارة:</label>
          <input type="text" id="editEmployeeDepartment" placeholder="الإدارة" readonly>
        </div>
      </div>

      <!-- الصف الثاني: قيمة السلفة، تاريخ السلفة، سبب السلفة، طريقة السداد -->
      <div class="form-row">
        <div class="form-group">
          <label for="editAdvanceAmount">قيمة السلفة:</label>
          <input type="number" id="editAdvanceAmount" placeholder="قيمة السلفة" min="0" step="1" required>
        </div>

        <div class="form-group">
          <label for="editAdvanceDate">تاريخ السلفة:</label>
          <input type="date" id="editAdvanceDate" required>
        </div>

        <div class="form-group">
          <label for="editAdvanceReason">سبب السلفة:</label>
          <input type="text" id="editAdvanceReason" placeholder="سبب السلفة" required>
        </div>

        <div class="form-group">
          <label for="editPaymentMethod">طريقة السداد:</label>
          <input type="text" id="editPaymentMethod" placeholder="طريقة السداد" required>
        </div>
      </div>

      <!-- الصف الثالث: الملاحظات -->
      <div class="form-row">
        <div class="form-group full-width">
          <label for="editNotes">ملاحظات إضافية:</label>
          <textarea id="editNotes" placeholder="ملاحظات إضافية" rows="3"></textarea>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <button type="button" id="updateAdvance" class="btn btn-primary">
        <i class="fas fa-save"></i>
        حفظ التغييرات
      </button>
      <button type="button" class="btn btn-secondary close-modal">
        <i class="fas fa-times"></i>
        إلغاء
      </button>
    </div>
  </div>
</div>

<!-- تضمين الملفات المطلوبة -->
<script src="shared-utils.js"></script>
<script src="dateUtils.js"></script>

<script src="salaryAdvance.js"></script>





</body>
</html>
