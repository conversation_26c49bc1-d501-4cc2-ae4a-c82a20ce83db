<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>قسم التدريب</title>
  <link rel="stylesheet" href="style.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="no-sidebar-layout.css">

  <!-- DataTables CSS -->
  <link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">
  <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
  <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.dataTables.min.css">

  <!-- تنسيقات خاصة بجدول الدورات التدريبية -->
  <link rel="stylesheet" href="training-datatables.css">

  <script src="permissions.js" defer></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

  <!-- jQuery and DataTables JS -->
  <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
  <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
  <script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
  <script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
  <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
</head>
<body class="training-page">

    <!-- زر العودة للوحة التحكم -->
  <div class="back-to-dashboard">
    <a href="training-cards.html" class="dashboard-btn">
      <i class="fas fa-arrow-right"></i>
      <span>العودة لإدارة التدريب</span>
    </a>
  </div>

  <div class="main-content full-width" id="mainContent">
    <h1>قسم التدريب</h1>


    <!-- تبويب إضافة دورة تدريبية -->
    <div class="tab-content" id="add-training" style="display: none;">
      <form class="training-form">
        <div class="form-group">
          <label for="employeeSearchAdd">البحث عن الموظف (بالاسم أو الكود):</label>
          <input type="text" id="employeeSearchAdd" class="employee-search-input" placeholder="أدخل اسم أو كود الموظف" list="employeeSearchSuggestions" autocomplete="off">
          <datalist id="employeeSearchSuggestions"></datalist>
          <small class="search-hint">اكتب كود الموظف أو جزء من الاسم ثم اختر من القائمة</small>
        </div>
        <div class="form-group">
          <label for="employeeCode">كود الموظف:</label>
          <input type="text" id="employeeCode" placeholder="كود الموظف" readonly>
        </div>
        <div class="form-group">
          <label for="employeeName">اسم الموظف:</label>
          <input type="text" id="employeeName" placeholder="اسم الموظف" readonly>
        </div>
        <div class="form-group">
          <label for="employeeDepartment">الإدارة:</label>
          <input type="text" id="employeeDepartment" placeholder="الإدارة" readonly>
        </div>
        <div class="form-group">
          <label for="courseName">اسم الدورة:</label>
          <input type="text" id="courseName" placeholder="أدخل اسم الدورة التدريبية" required>
        </div>
        <div class="form-group">
          <label for="courseDate">تاريخ الدورة:</label>
          <input type="date" id="courseDate" required>
        </div>
        <div class="form-group">
          <label for="courseDuration">مدة الدورة (بالأيام):</label>
          <input type="number" id="courseDuration" placeholder="عدد أيام الدورة" min="1" required>
        </div>
        <div class="form-group">
          <label for="trainingType">نوع التدريب:</label>
          <select id="trainingType" required>
            <option value="">اختر نوع التدريب</option>
            <option value="تدريب داخلي">تدريب داخلي</option>
            <option value="تدريب خارجي">تدريب خارجي</option>
            <option value="تدريب خاص">تدريب خاص</option>
          </select>
        </div>
        <div class="form-group">
          <label for="courseCost">تكلفة الدورة:</label>
          <input type="number" id="courseCost" placeholder="تكلفة الدورة" min="0" step="0.01">
        </div>
        <div class="form-group full-width">
          <label for="trainingNotes">ملاحظات:</label>
          <textarea id="trainingNotes" rows="3" placeholder="ملاحظات إضافية (اختياري)"></textarea>
        </div>
        <div class="form-actions">
          <button id="saveTraining" class="save-btn" type="button">حفظ الدورة التدريبية</button>
          <button id="resetTrainingForm" class="reset-btn" type="button">إعادة تعيين</button>
        </div>
      </form>
      <!-- جدول الدورات التدريبية -->
      <div class="training-table-container">
        <h3>قائمة الدورات التدريبية</h3>

        <!-- فلاتر البحث المتقدمة -->
        <div class="advanced-search-container">
          <h4>فلاتر البحث المتقدمة</h4>

          <!-- الصف الأول من الفلاتر -->
          <div class="search-filters-row">
            <div class="filter-group">
              <label for="searchTrainingId">رقم السجل:</label>
              <input type="text" id="searchTrainingId" class="filter-input" placeholder="رقم السجل">
            </div>

            <div class="filter-group">
              <label for="searchTrainingEmployeeCode">كود الموظف:</label>
              <input type="text" id="searchTrainingEmployeeCode" class="filter-input" placeholder="كود الموظف">
            </div>

            <div class="filter-group">
              <label for="searchTrainingEmployeeName">اسم الموظف:</label>
              <input type="text" id="searchTrainingEmployeeName" class="filter-input" placeholder="اسم الموظف">
            </div>

            <div class="filter-group">
              <label for="searchTrainingDepartment">الإدارة:</label>
              <select id="searchTrainingDepartment" class="filter-input">
                <option value="">جميع الإدارات</option>
              </select>
            </div>
          </div>

          <!-- الصف الثاني من الفلاتر -->
          <div class="search-filters-row">
            <div class="filter-group">
              <label for="searchTrainingCourseName">اسم الدورة:</label>
              <input type="text" id="searchTrainingCourseName" class="filter-input" placeholder="اسم الدورة">
            </div>

            <div class="filter-group">
              <label for="searchTrainingType">نوع التدريب:</label>
              <select id="searchTrainingType" class="filter-input">
                <option value="">جميع الأنواع</option>
                <option value="تدريب داخلي">تدريب داخلي</option>
                <option value="تدريب خارجي">تدريب خارجي</option>
                <option value="تدريب خاص">تدريب خاص</option>
              </select>
            </div>

            <div class="filter-group">
              <label for="searchTrainingDateFrom">من تاريخ:</label>
              <input type="date" id="searchTrainingDateFrom" class="filter-input">
            </div>

            <div class="filter-group">
              <label for="searchTrainingDateTo">إلى تاريخ:</label>
              <input type="date" id="searchTrainingDateTo" class="filter-input">
            </div>
          </div>

          <!-- الصف الثالث من الفلاتر -->
          <div class="search-filters-row">
            <div class="filter-group">
              <label for="searchTrainingMinCost">أقل تكلفة:</label>
              <input type="number" id="searchTrainingMinCost" class="filter-input" placeholder="0" min="0" step="0.01">
            </div>

            <div class="filter-group">
              <label for="searchTrainingMaxCost">أعلى تكلفة:</label>
              <input type="number" id="searchTrainingMaxCost" class="filter-input" placeholder="0" min="0" step="0.01">
            </div>

            <div class="filter-group">
              <label for="searchTrainingMinDuration">أقل مدة (أيام):</label>
              <input type="number" id="searchTrainingMinDuration" class="filter-input" placeholder="1" min="1">
            </div>

            <div class="filter-group">
              <label for="searchTrainingMaxDuration">أعلى مدة (أيام):</label>
              <input type="number" id="searchTrainingMaxDuration" class="filter-input" placeholder="1" min="1">
            </div>
          </div>

          <!-- أزرار الفلاتر -->
          <div class="filter-actions">
            <button type="button" id="applyTrainingFilters" class="btn btn-primary">تطبيق الفلاتر</button>
            <button type="button" id="clearTrainingFilters" class="btn btn-secondary">مسح الفلاتر</button>
          </div>
        </div>

        <table id="addedTrainingTable" class="added-training-table display nowrap" style="width:100%">
          <thead>
            <tr>
              <th>رقم السجل</th>
              <th>كود الموظف</th>
              <th>اسم الموظف</th>
              <th>الإدارة</th>
              <th>اسم الدورة</th>
              <th>تاريخ الدورة</th>
              <th>مدة الدورة</th>
              <th>نوع التدريب</th>
              <th>التكلفة</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody></tbody>
        </table>
      </div>
    </div>

    <!-- تبويب تقارير التدريب -->
    <div class="tab-content" id="training-reports" style="display: none;">
      <div class="reports-section">
        <h3>تقارير التدريب</h3>
        
        <div class="report-filters">
          <div class="filter-group">
            <label for="reportDepartment">الإدارة:</label>
            <select id="reportDepartment">
              <option value="">جميع الإدارات</option>
            </select>
          </div>

          <div class="filter-group">
            <label for="reportTrainingType">نوع التدريب:</label>
            <select id="reportTrainingType">
              <option value="">جميع الأنواع</option>
              <option value="تدريب داخلي">تدريب داخلي</option>
              <option value="تدريب خارجي">تدريب خارجي</option>
              <option value="تدريب خاص">تدريب خاص</option>
            </select>
          </div>

          <div class="filter-group">
            <label for="reportStartDate">من تاريخ:</label>
            <input type="date" id="reportStartDate">
          </div>

          <div class="filter-group">
            <label for="reportEndDate">إلى تاريخ:</label>
            <input type="date" id="reportEndDate">
          </div>

          <button id="generateReport" class="generate-btn">إنشاء التقرير</button>
          <button id="exportReportBtn" class="export-btn">تصدير التقرير</button>
        </div>

        <div class="report-results">
          <div class="report-summary">
            <div class="summary-card">
              <h4>إجمالي الدورات</h4>
              <span id="totalCourses">0</span>
            </div>
            <div class="summary-card">
              <h4>إجمالي التكلفة</h4>
              <span id="totalCost">0</span>
            </div>
            <div class="summary-card">
              <h4>متوسط مدة الدورة</h4>
              <span id="avgDuration">0</span>
            </div>
          </div>
          
          <table class="report-table" id="report-table">
            <thead>
              <tr>
                <th>كود الموظف</th>
                <th>اسم الموظف</th>
                <th>الإدارة</th>
                <th>اسم الدورة</th>
                <th>تاريخ الدورة</th>
                <th>مدة الدورة</th>
                <th>نوع التدريب</th>
                <th>التكلفة</th>
              </tr>
            </thead>
            <tbody>
              <!-- سيتم ملء البيانات هنا بواسطة JavaScript -->
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <!-- نافذة تعديل الدورة التدريبية -->
  <div class="modal-overlay" id="editTrainingModal" style="display: none;" onclick="closeEditModal()">
    <div class="modal-content" onclick="event.stopPropagation()">
      <div class="modal-header">
        <h3>تعديل الدورة التدريبية</h3>
        <button class="modal-close" onclick="closeEditModal()">&times;</button>
      </div>

      <div class="modal-body">
        <form id="editTrainingForm">
          <!-- البحث عن الموظف -->
          <div class="form-row">
            <div class="form-group full-width">
              <label for="editEmployeeSearch">البحث عن الموظف (بالاسم أو الكود):</label>
              <input type="text" id="editEmployeeSearch" class="employee-search-input" placeholder="أدخل اسم أو كود الموظف" list="editEmployeeSearchSuggestions" autocomplete="off">
              <datalist id="editEmployeeSearchSuggestions"></datalist>
              <small class="search-hint">اكتب كود الموظف أو جزء من الاسم ثم اختر من القائمة</small>
            </div>
          </div>

          <!-- الصف الأول: كود الموظف، اسم الموظف، الإدارة -->
          <div class="form-row">
            <div class="form-group">
              <label for="editEmployeeCode">كود الموظف:</label>
              <input type="text" id="editEmployeeCode" placeholder="كود الموظف" readonly>
            </div>

            <div class="form-group">
              <label for="editEmployeeName">اسم الموظف:</label>
              <input type="text" id="editEmployeeName" placeholder="اسم الموظف" readonly>
            </div>

            <div class="form-group">
              <label for="editEmployeeDepartment">الإدارة:</label>
              <input type="text" id="editEmployeeDepartment" placeholder="الإدارة" readonly>
            </div>
          </div>

          <!-- الصف الثاني: اسم الدورة، تاريخ الدورة، مدة الدورة -->
          <div class="form-row">
            <div class="form-group">
              <label for="editCourseName">اسم الدورة:</label>
              <input type="text" id="editCourseName" placeholder="أدخل اسم الدورة التدريبية" required>
            </div>

            <div class="form-group">
              <label for="editCourseDate">تاريخ الدورة:</label>
              <input type="date" id="editCourseDate" required>
            </div>

            <div class="form-group">
              <label for="editCourseDuration">مدة الدورة (بالأيام):</label>
              <input type="number" id="editCourseDuration" placeholder="عدد أيام الدورة" min="1" required>
            </div>
          </div>

          <!-- الصف الثالث: نوع التدريب، تكلفة الدورة -->
          <div class="form-row">
            <div class="form-group">
              <label for="editTrainingType">نوع التدريب:</label>
              <select id="editTrainingType" required>
                <option value="">اختر نوع التدريب</option>
                <option value="تدريب داخلي">تدريب داخلي</option>
                <option value="تدريب خارجي">تدريب خارجي</option>
                <option value="تدريب خاص">تدريب خاص</option>
              </select>
            </div>

            <div class="form-group">
              <label for="editCourseCost">تكلفة الدورة:</label>
              <input type="number" id="editCourseCost" placeholder="تكلفة الدورة" min="0" step="1">
            </div>

            <div class="form-group"></div> <!-- حقل فارغ للتوازن -->
          </div>

          <!-- الصف الرابع: الملاحظات -->
          <div class="form-row">
            <div class="form-group full-width">
              <label for="editTrainingNotes">ملاحظات:</label>
              <textarea id="editTrainingNotes" rows="3" placeholder="ملاحظات إضافية (اختياري)"></textarea>
            </div>
          </div>
        </form>
      </div>

      <div class="modal-footer">
        <button id="updateTraining" class="save-btn">تحديث الدورة التدريبية</button>
        <button type="button" class="cancel-btn" onclick="closeEditModal()">إلغاء</button>
      </div>
    </div>
  </div>

  <script src="dateUtils.js"></script>
  <script src="form-validation.js"></script>
  <script src="training.js"></script>
  
</body>
</html>
