// متغيرات عامة
const API_URL = 'http://localhost:5500/api';
let employees = [];
let trainingCourses = [];
let filteredCourses = [];

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
  initializeTrainingPage();
});

// تهيئة صفحة التدريب
async function initializeTrainingPage() {
  try {
    await loadEmployees();
    await loadTrainingCourses();
    setupTrainingEventListeners();
    loadDepartments();

    // التحقق من المحتوى المحدد
    setTimeout(() => {
      checkSelectedContent();
    }, 100);
  } catch (error) {
    console.error('خطأ في تهيئة صفحة التدريب:', error);
    alert('حدث خطأ في تحميل البيانات');
  }
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
  // البحث عن الموظف
  const employeeSearchInput = document.getElementById('employeeSearchAdd');
  if (employeeSearchInput) {
    employeeSearchInput.addEventListener('input', handleEmployeeSearch);
    employeeSearchInput.addEventListener('change', handleEmployeeSelection);

    // إضافة مستمع للضغط على Enter
    employeeSearchInput.addEventListener('keydown', function(e) {
      if (e.key === 'Enter') {
        e.preventDefault();
        handleEmployeeSelection.call(this);
      }
    });

    // إضافة مستمع لفقدان التركيز مع تأخير قصير
    employeeSearchInput.addEventListener('blur', function() {
      setTimeout(() => {
        handleEmployeeSelection.call(this);
      }, 100);
    });
  }

  // حفظ الدورة التدريبية
  const saveTrainingBtn = document.getElementById('saveTraining');
  if (saveTrainingBtn) {
    saveTrainingBtn.addEventListener('click', saveTrainingCourse);
  }

  // إعادة تعيين النموذج
  const resetFormBtn = document.getElementById('resetTrainingForm');
  if (resetFormBtn) {
    resetFormBtn.addEventListener('click', resetTrainingForm);
  }

  // البحث في الجدول
  const searchInput = document.getElementById('searchTraining');
  if (searchInput) {
    searchInput.addEventListener('input', function() {
      searchInTrainingTable(this.value);
    });
  }

  // تصدير البيانات
  const exportBtn = document.getElementById('exportTrainingBtn');
  if (exportBtn) {
    exportBtn.addEventListener('click', exportTrainingData);
  }

  // إنشاء التقرير
  const generateReportBtn = document.getElementById('generateReport');
  if (generateReportBtn) {
    generateReportBtn.addEventListener('click', generateTrainingReport);
  }

  // تصدير التقرير
  const exportReportBtn = document.getElementById('exportReportBtn');
  if (exportReportBtn) {
    exportReportBtn.addEventListener('click', exportTrainingReport);
  }

  // إعداد النافذة المنبثقة للتعديل
  setupEditModal();
}

// إعداد النافذة المنبثقة للتعديل
function setupEditModal() {
  // البحث عن الموظف في النافذة المنبثقة
  const editEmployeeSearchInput = document.getElementById('editEmployeeSearch');
  if (editEmployeeSearchInput) {
    editEmployeeSearchInput.addEventListener('input', handleEditEmployeeSearch);
    editEmployeeSearchInput.addEventListener('change', handleEditEmployeeSelection);

    editEmployeeSearchInput.addEventListener('keydown', function(e) {
      if (e.key === 'Enter') {
        e.preventDefault();
        handleEditEmployeeSelection.call(this);
      }
    });

    editEmployeeSearchInput.addEventListener('blur', function() {
      setTimeout(() => {
        handleEditEmployeeSelection.call(this);
      }, 100);
    });
  }

  // تحديث الدورة التدريبية
  const updateTrainingBtn = document.getElementById('updateTraining');
  if (updateTrainingBtn) {
    updateTrainingBtn.addEventListener('click', updateTrainingCourse);
  }

  // إغلاق النافذة المنبثقة بمفتاح Escape
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
      const modal = document.getElementById('editTrainingModal');
      if (modal && modal.style.display === 'flex') {
        closeEditModal();
      }
    }
  });
}

// التحقق من المحتوى المحدد من URL
function checkSelectedContent() {
  // قراءة التبويب من URL
  const urlParams = new URLSearchParams(window.location.search);
  const tabFromUrl = urlParams.get('tab');

  if (tabFromUrl) {
    // تحويل اسم التبويب من URL إلى اسم المحتوى
    let contentType = '';
    switch(tabFromUrl) {
      case 'add-training':
        contentType = 'add-training';
        break;
      case 'training-reports':
        contentType = 'training-reports';
        break;
      default:
        contentType = 'add-training';
    }
    showContent(contentType);
  } else {
    // عرض المحتوى الافتراضي (إضافة دورة تدريبية)
    showContent('add-training');
  }
}

// عرض المحتوى المحدد
function showContent(contentType) {
  console.log('عرض المحتوى:', contentType);

  // إخفاء جميع المحتويات
  const allContents = document.querySelectorAll('.tab-content');
  allContents.forEach(content => {
    content.style.display = 'none';
  });

  // عرض المحتوى المحدد
  const targetContent = document.getElementById(contentType);
  if (targetContent) {
    targetContent.style.display = 'block';

    // تحديث عنوان الصفحة
    const pageTitle = document.querySelector('h1');
    if (pageTitle) {
      if (contentType === 'add-training') {
        pageTitle.textContent = 'إضافة دورة تدريبية';
      } else if (contentType === 'training-reports') {
        pageTitle.textContent = 'تقارير التدريب';
      }
    }

    // تحديث البيانات حسب نوع المحتوى
    if (contentType === 'training-reports') {
      loadDepartments();
    }
  } else {
    console.error('لم يتم العثور على المحتوى:', contentType);
  }
}

// تحميل قائمة الموظفين
async function loadEmployees() {
  try {
    console.log('بدء تحميل الموظفين...');
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/employees`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (response.ok) {
      employees = await response.json();
      console.log('تم تحميل الموظفين بنجاح:', employees.length);
      console.log('أول موظف:', employees[0]);
    } else {
      console.error('فشل في تحميل الموظفين - كود الاستجابة:', response.status);
      const errorText = await response.text();
      console.error('رسالة الخطأ:', errorText);
    }
  } catch (error) {
    console.error('خطأ في تحميل الموظفين:', error);
    alert('حدث خطأ في تحميل قائمة الموظفين. يرجى التحقق من الاتصال بالخادم.');
  }
}

// تحميل الدورات التدريبية
async function loadTrainingCourses() {
  try {
    console.log('بدء تحميل الدورات التدريبية...');
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/training`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (response.ok) {
      trainingCourses = await response.json();
      filteredCourses = [...trainingCourses];
      displayTrainingCourses();
      console.log('تم تحميل الدورات التدريبية بنجاح:', trainingCourses.length);
    } else {
      console.error('فشل في تحميل الدورات التدريبية - كود الاستجابة:', response.status);
      // إنشاء جدول التدريب إذا لم يكن موجوداً
      await createTrainingTable();
    }
  } catch (error) {
    console.error('خطأ في تحميل الدورات التدريبية:', error);
    // إنشاء جدول التدريب إذا لم يكن موجوداً
    await createTrainingTable();
  }
}

// إنشاء جدول التدريب
async function createTrainingTable() {
  try {
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/training/create-table`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (response.ok) {
      console.log('تم إنشاء جدول التدريب');
      await loadTrainingCourses();
    }
  } catch (error) {
    console.error('خطأ في إنشاء جدول التدريب:', error);
  }
}

// البحث عن الموظف
function handleEmployeeSearch() {
  const searchValue = this.value.toLowerCase().trim();
  const datalist = document.getElementById('employeeSearchSuggestions');

  if (!datalist) return;

  datalist.innerHTML = '';

  if (searchValue.length < 1) {
    // إذا كان البحث فارغ، نفرغ الحقول
    document.getElementById('employeeCode').value = '';
    document.getElementById('employeeName').value = '';
    document.getElementById('employeeDepartment').value = '';
    return;
  }

  // فلترة الموظفين النشطين فقط
  const filteredEmployees = employees.filter(emp => {
    if (emp.status === 'مستقيل') return false;

    const codeMatch = emp.code.toString().includes(searchValue);
    const nameMatch = emp.full_name.toLowerCase().includes(searchValue);
    const deptMatch = emp.department && emp.department.toLowerCase().includes(searchValue);

    return codeMatch || nameMatch || deptMatch;
  });

  // ترتيب النتائج: التطابق الدقيق للكود أولاً، ثم بداية الاسم، ثم باقي النتائج
  filteredEmployees.sort((a, b) => {
    const aCodeExact = a.code.toString() === searchValue;
    const bCodeExact = b.code.toString() === searchValue;

    if (aCodeExact && !bCodeExact) return -1;
    if (!aCodeExact && bCodeExact) return 1;

    const aCodeStart = a.code.toString().startsWith(searchValue);
    const bCodeStart = b.code.toString().startsWith(searchValue);

    if (aCodeStart && !bCodeStart) return -1;
    if (!aCodeStart && bCodeStart) return 1;

    const aNameStart = a.full_name.toLowerCase().startsWith(searchValue);
    const bNameStart = b.full_name.toLowerCase().startsWith(searchValue);

    if (aNameStart && !bNameStart) return -1;
    if (!aNameStart && bNameStart) return 1;

    return a.full_name.localeCompare(b.full_name, 'ar');
  });

  // عرض أفضل 10 نتائج
  filteredEmployees.slice(0, 10).forEach(emp => {
    const option = document.createElement('option');
    option.value = `${emp.code} - ${emp.full_name}`;
    option.dataset.code = emp.code;
    option.dataset.name = emp.full_name;
    option.dataset.department = emp.department || '';
    datalist.appendChild(option);
  });

  console.log('Search results:', filteredEmployees.length, 'employees found for:', searchValue);
}

// اختيار الموظف
function handleEmployeeSelection() {
  const selectedValue = this.value.trim();
  console.log('Selected value:', selectedValue);

  // إذا كان الحقل فارغ، نفرغ جميع الحقول
  if (selectedValue === '') {
    document.getElementById('employeeCode').value = '';
    document.getElementById('employeeName').value = '';
    document.getElementById('employeeDepartment').value = '';
    return;
  }

  // البحث في datalist أولاً بطريقة دقيقة
  const datalistOptions = document.querySelectorAll('#employeeSearchSuggestions option');
  let foundOption = null;

  for (let option of datalistOptions) {
    if (option.value === selectedValue) {
      foundOption = option;
      break;
    }
  }

  if (foundOption) {
    console.log('Found exact option:', foundOption);
    document.getElementById('employeeCode').value = foundOption.dataset.code;
    document.getElementById('employeeName').value = foundOption.dataset.name;
    document.getElementById('employeeDepartment').value = foundOption.dataset.department || '';
    return;
  }

  // إذا لم نجد تطابق دقيق، نحاول استخراج الكود من بداية النص
  const codeMatch = selectedValue.match(/^(\d+)/);
  if (codeMatch) {
    const extractedCode = codeMatch[1];
    const foundEmployee = employees.find(emp => emp.code.toString() === extractedCode);

    if (foundEmployee) {
      console.log('Found employee by extracted code:', foundEmployee);
      document.getElementById('employeeCode').value = foundEmployee.code;
      document.getElementById('employeeName').value = foundEmployee.full_name;
      document.getElementById('employeeDepartment').value = foundEmployee.department || '';

      // تحديث قيمة البحث لتظهر بالتنسيق الصحيح
      this.value = `${foundEmployee.code} - ${foundEmployee.full_name}`;
      return;
    }
  }

  // البحث المباشر بالكود فقط
  const directCodeEmployee = employees.find(emp => emp.code.toString() === selectedValue);
  if (directCodeEmployee) {
    console.log('Found employee by direct code:', directCodeEmployee);
    document.getElementById('employeeCode').value = directCodeEmployee.code;
    document.getElementById('employeeName').value = directCodeEmployee.full_name;
    document.getElementById('employeeDepartment').value = directCodeEmployee.department || '';

    this.value = `${directCodeEmployee.code} - ${directCodeEmployee.full_name}`;
    return;
  }

  console.log('No exact match found for:', selectedValue);
}

// البحث عن الموظف في النافذة المنبثقة
function handleEditEmployeeSearch() {
  const searchValue = this.value.toLowerCase().trim();
  const datalist = document.getElementById('editEmployeeSearchSuggestions');

  if (!datalist) return;

  datalist.innerHTML = '';

  if (searchValue.length < 1) {
    document.getElementById('editEmployeeCode').value = '';
    document.getElementById('editEmployeeName').value = '';
    document.getElementById('editEmployeeDepartment').value = '';
    return;
  }

  const filteredEmployees = employees.filter(emp => {
    if (emp.status === 'مستقيل') return false;

    const codeMatch = emp.code.toString().includes(searchValue);
    const nameMatch = emp.full_name.toLowerCase().includes(searchValue);
    const deptMatch = emp.department && emp.department.toLowerCase().includes(searchValue);

    return codeMatch || nameMatch || deptMatch;
  });

  filteredEmployees.sort((a, b) => {
    const aCodeExact = a.code.toString() === searchValue;
    const bCodeExact = b.code.toString() === searchValue;

    if (aCodeExact && !bCodeExact) return -1;
    if (!aCodeExact && bCodeExact) return 1;

    const aCodeStart = a.code.toString().startsWith(searchValue);
    const bCodeStart = b.code.toString().startsWith(searchValue);

    if (aCodeStart && !bCodeStart) return -1;
    if (!aCodeStart && bCodeStart) return 1;

    const aNameStart = a.full_name.toLowerCase().startsWith(searchValue);
    const bNameStart = b.full_name.toLowerCase().startsWith(searchValue);

    if (aNameStart && !bNameStart) return -1;
    if (!aNameStart && bNameStart) return 1;

    return a.full_name.localeCompare(b.full_name, 'ar');
  });

  filteredEmployees.slice(0, 10).forEach(emp => {
    const option = document.createElement('option');
    option.value = `${emp.code} - ${emp.full_name}`;
    option.dataset.code = emp.code;
    option.dataset.name = emp.full_name;
    option.dataset.department = emp.department || '';
    datalist.appendChild(option);
  });

  console.log('Edit modal search results:', filteredEmployees.length, 'employees found for:', searchValue);
}

// اختيار الموظف في النافذة المنبثقة
function handleEditEmployeeSelection() {
  const selectedValue = this.value.trim();
  console.log('Edit modal selected value:', selectedValue);

  if (selectedValue === '') {
    document.getElementById('editEmployeeCode').value = '';
    document.getElementById('editEmployeeName').value = '';
    document.getElementById('editEmployeeDepartment').value = '';
    return;
  }

  const datalistOptions = document.querySelectorAll('#editEmployeeSearchSuggestions option');
  let foundOption = null;

  for (let option of datalistOptions) {
    if (option.value === selectedValue) {
      foundOption = option;
      break;
    }
  }

  if (foundOption) {
    console.log('Edit modal found exact option:', foundOption);
    document.getElementById('editEmployeeCode').value = foundOption.dataset.code;
    document.getElementById('editEmployeeName').value = foundOption.dataset.name;
    document.getElementById('editEmployeeDepartment').value = foundOption.dataset.department || '';
    return;
  }

  const codeMatch = selectedValue.match(/^(\d+)/);
  if (codeMatch) {
    const extractedCode = codeMatch[1];
    const foundEmployee = employees.find(emp => emp.code.toString() === extractedCode);

    if (foundEmployee) {
      console.log('Edit modal found employee by extracted code:', foundEmployee);
      document.getElementById('editEmployeeCode').value = foundEmployee.code;
      document.getElementById('editEmployeeName').value = foundEmployee.full_name;
      document.getElementById('editEmployeeDepartment').value = foundEmployee.department || '';
      this.value = `${foundEmployee.code} - ${foundEmployee.full_name}`;
      return;
    }
  }

  const directCodeEmployee = employees.find(emp => emp.code.toString() === selectedValue);
  if (directCodeEmployee) {
    console.log('Edit modal found employee by direct code:', directCodeEmployee);
    document.getElementById('editEmployeeCode').value = directCodeEmployee.code;
    document.getElementById('editEmployeeName').value = directCodeEmployee.full_name;
    document.getElementById('editEmployeeDepartment').value = directCodeEmployee.department || '';
    this.value = `${directCodeEmployee.code} - ${directCodeEmployee.full_name}`;
    return;
  }

  console.log('Edit modal: No exact match found for:', selectedValue);
}

// وظيفة للتحقق من صحة بيانات الموظف المختار
function validateSelectedEmployee() {
  const employeeCode = document.getElementById('employeeCode').value;
  const employeeName = document.getElementById('employeeName').value;
  const searchValue = document.getElementById('employeeSearchAdd').value;

  if (employeeCode && employeeName) {
    // التحقق من أن البيانات المعروضة تطابق الموظف الصحيح
    const employee = employees.find(emp => emp.code.toString() === employeeCode);
    if (employee && employee.full_name === employeeName) {
      console.log('Employee data validated successfully');
      return true;
    } else {
      console.warn('Employee data mismatch detected');
      // إعادة تعيين البيانات
      document.getElementById('employeeCode').value = '';
      document.getElementById('employeeName').value = '';
      document.getElementById('employeeDepartment').value = '';
      return false;
    }
  }
  return false;
}

// حفظ الدورة التدريبية
async function saveTrainingCourse() {
  const employeeCode = document.getElementById('employeeCode').value;
  const employeeName = document.getElementById('employeeName').value;
  const department = document.getElementById('employeeDepartment').value;
  const courseName = document.getElementById('courseName').value;
  const courseDate = document.getElementById('courseDate').value;
  const courseDuration = document.getElementById('courseDuration').value;
  const trainingType = document.getElementById('trainingType').value;
  const courseCost = document.getElementById('courseCost').value || 0;
  const notes = document.getElementById('trainingNotes').value;

  // التحقق من صحة البيانات الأساسية
  if (!employeeCode || !employeeName || !courseName || !courseDate || !courseDuration || !trainingType) {
    alert('يرجى ملء جميع الحقول المطلوبة');
    return;
  }

  // التحقق من صحة بيانات الموظف المختار
  const selectedEmployee = employees.find(emp => emp.code.toString() === employeeCode);
  if (!selectedEmployee) {
    alert('الموظف المختار غير صحيح. يرجى البحث واختيار موظف صحيح.');
    return;
  }

  if (selectedEmployee.full_name !== employeeName) {
    alert('بيانات الموظف غير متطابقة. يرجى البحث واختيار الموظف مرة أخرى.');
    // إعادة تعيين بيانات الموظف
    document.getElementById('employeeCode').value = selectedEmployee.code;
    document.getElementById('employeeName').value = selectedEmployee.full_name;
    document.getElementById('employeeDepartment').value = selectedEmployee.department || '';
    document.getElementById('employeeSearchAdd').value = `${selectedEmployee.code} - ${selectedEmployee.full_name}`;
    return;
  }

  const trainingData = {
    employee_code: employeeCode,
    employee_name: employeeName,
    department: department,
    course_name: courseName,
    course_date: courseDate,
    course_duration: parseInt(courseDuration),
    training_type: trainingType,
    course_cost: parseFloat(courseCost),
    notes: notes
  };

  try {
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/training`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(trainingData)
    });

    if (response.ok) {
      alert('تم حفظ الدورة التدريبية بنجاح');

      // إعادة تحميل البيانات في DataTables إذا كان مهيأ
      if (window.addedTrainingDataTable) {
        window.addedTrainingDataTable.ajax.reload();
      } else {
        await loadTrainingCourses();
      }

      resetTrainingForm();
    } else {
      const errorData = await response.json();
      alert(errorData.error || 'فشل في حفظ الدورة التدريبية');
    }
  } catch (error) {
    console.error('خطأ في حفظ الدورة التدريبية:', error);
    alert('حدث خطأ أثناء حفظ الدورة التدريبية');
  }
}

// إعادة تعيين النموذج
function resetTrainingForm() {
  document.getElementById('employeeSearchAdd').value = '';
  document.getElementById('employeeCode').value = '';
  document.getElementById('employeeName').value = '';
  document.getElementById('employeeDepartment').value = '';
  document.getElementById('courseName').value = '';
  document.getElementById('courseDate').value = '';
  document.getElementById('courseDuration').value = '';
  document.getElementById('trainingType').value = '';
  document.getElementById('courseCost').value = '';
  document.getElementById('trainingNotes').value = '';

  // تفريغ datalist
  const datalist = document.getElementById('employeeSearchSuggestions');
  if (datalist) {
    datalist.innerHTML = '';
  }

  const saveBtn = document.getElementById('saveTraining');
  saveBtn.textContent = 'حفظ الدورة التدريبية';
}

// عرض الدورات التدريبية (محدثة للعمل مع DataTables)
function displayTrainingCourses() {
  // إذا كان DataTables مهيأ، قم بإعادة تحميل البيانات
  if (window.addedTrainingDataTable) {
    window.addedTrainingDataTable.ajax.reload();
    return;
  }

  // الكود القديم للجدول العادي (احتياطي)
  const tableBody = document.querySelector('#training-table tbody');
  if (!tableBody) return;

  tableBody.innerHTML = '';

  // ترتيب البيانات حسب تاريخ الإنشاء (الأحدث أولاً)
  const sortedCourses = [...filteredCourses].sort((a, b) => {
    // ترتيب حسب created_at إذا كان متوفراً، وإلا حسب id
    const aTime = a.created_at ? new Date(a.created_at).getTime() : (a.id || 0);
    const bTime = b.created_at ? new Date(b.created_at).getTime() : (b.id || 0);
    return bTime - aTime; // الأحدث أولاً
  });

  sortedCourses.forEach((course, index) => {
    const row = document.createElement('tr');

    // تمييز الصف الأول (آخر إضافة) بلون أخضر فاتح
    if (index === 0) {
      row.style.backgroundColor = '#e8f5e8';
      row.style.border = '2px solid #4CAF50';
    }

    row.innerHTML = `
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${course.employee_code}</td>
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${course.employee_name}</td>
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${course.department || ''}</td>
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${course.course_name}</td>
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${formatDate(course.course_date)}</td>
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${course.course_duration} يوم</td>
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${course.training_type}</td>
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${course.course_cost} جنيه</td>
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${course.notes || ''}</td>
      <td>
        ${hasPermission('edit_training') ? `<button class="edit-btn" onclick="editTrainingCourse(${course.id})" title="تعديل">
          <i class="fas fa-edit"></i>
        </button>` : ''}
        ${hasPermission('delete_training') ? `<button class="delete-btn" onclick="deleteTrainingCourse(${course.id})" title="حذف">
          <i class="fas fa-trash-alt"></i>
        </button>` : ''}
      </td>
    `;
    tableBody.appendChild(row);
  });
}

// فحص الصلاحيات
function hasPermission(permission) {
  try {
    const permissions = JSON.parse(localStorage.getItem('permissions') || '{}');
    const result = permissions[permission] === true;
    console.log(`[Training] hasPermission(${permission}) = ${result}`, permissions);
    return result;
  } catch (error) {
    console.error('خطأ في قراءة الصلاحيات:', error);
    return false;
  }
}

// تعديل الدورة التدريبية - فتح النافذة المنبثقة
async function editTrainingCourse(id) {
  // فحص صلاحية التعديل
  if (!hasPermission('edit_training')) {
    alert('ليس لديك صلاحية لتعديل الدورات التدريبية');
    return;
  }

  // تحويل id إلى رقم للتأكد
  const courseId = parseInt(id);

  try {
    // الحصول على بيانات الدورة من الخادم
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/training/${courseId}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw new Error('فشل في تحميل بيانات الدورة');
    }

    const course = await response.json();

    // ملء بيانات النافذة المنبثقة
    document.getElementById('editEmployeeSearch').value = `${course.employee_code} - ${course.employee_name}`;
    document.getElementById('editEmployeeCode').value = course.employee_code;
    document.getElementById('editEmployeeName').value = course.employee_name;
    document.getElementById('editEmployeeDepartment').value = course.department || '';
    document.getElementById('editCourseName').value = course.course_name;
    // استخدام DateUtils لتنسيق التاريخ بشكل صحيح
    if (typeof DateUtils !== 'undefined') {
      document.getElementById('editCourseDate').value = DateUtils.formatDateForInput(course.course_date);
    } else {
      document.getElementById('editCourseDate').value = formatDateForInput(course.course_date);
    }
    document.getElementById('editCourseDuration').value = course.course_duration;
    document.getElementById('editTrainingType').value = course.training_type;
    document.getElementById('editCourseCost').value = course.course_cost;
    document.getElementById('editTrainingNotes').value = course.notes || '';

    // حفظ معرف الدورة للتحديث
    const updateBtn = document.getElementById('updateTraining');
    updateBtn.dataset.editId = courseId;

    // إظهار النافذة المنبثقة
    document.getElementById('editTrainingModal').style.display = 'flex';

  } catch (error) {
    console.error('خطأ في تحميل بيانات الدورة:', error);
    alert('حدث خطأ في تحميل بيانات الدورة');
  }
}

// إغلاق النافذة المنبثقة
function closeEditModal() {
  document.getElementById('editTrainingModal').style.display = 'none';

  // تفريغ النموذج
  document.getElementById('editEmployeeSearch').value = '';
  document.getElementById('editEmployeeCode').value = '';
  document.getElementById('editEmployeeName').value = '';
  document.getElementById('editEmployeeDepartment').value = '';
  document.getElementById('editCourseName').value = '';
  document.getElementById('editCourseDate').value = '';
  document.getElementById('editCourseDuration').value = '';
  document.getElementById('editTrainingType').value = '';
  document.getElementById('editCourseCost').value = '';
  document.getElementById('editTrainingNotes').value = '';

  // تفريغ datalist
  const datalist = document.getElementById('editEmployeeSearchSuggestions');
  if (datalist) {
    datalist.innerHTML = '';
  }

  // إزالة معرف التحديث
  const updateBtn = document.getElementById('updateTraining');
  delete updateBtn.dataset.editId;
}

// تحديث الدورة التدريبية
async function updateTrainingCourse() {
  const updateBtn = document.getElementById('updateTraining');
  const editId = updateBtn.dataset.editId;

  if (!editId) {
    alert('خطأ: لم يتم تحديد الدورة للتحديث');
    return;
  }

  const employeeCode = document.getElementById('editEmployeeCode').value;
  const employeeName = document.getElementById('editEmployeeName').value;
  const department = document.getElementById('editEmployeeDepartment').value;
  const courseName = document.getElementById('editCourseName').value;
  const courseDate = document.getElementById('editCourseDate').value;
  const courseDuration = document.getElementById('editCourseDuration').value;
  const trainingType = document.getElementById('editTrainingType').value;
  const courseCost = document.getElementById('editCourseCost').value || 0;
  const notes = document.getElementById('editTrainingNotes').value;

  // التحقق من صحة البيانات الأساسية
  if (!employeeCode || !employeeName || !courseName || !courseDate || !courseDuration || !trainingType) {
    alert('يرجى ملء جميع الحقول المطلوبة');
    return;
  }

  // التحقق من صحة بيانات الموظف المختار
  const selectedEmployee = employees.find(emp => emp.code.toString() === employeeCode);
  if (!selectedEmployee) {
    alert('الموظف المختار غير صحيح. يرجى البحث واختيار موظف صحيح.');
    return;
  }

  if (selectedEmployee.full_name !== employeeName) {
    alert('بيانات الموظف غير متطابقة. يرجى البحث واختيار الموظف مرة أخرى.');
    return;
  }

  const trainingData = {
    employee_code: employeeCode,
    employee_name: employeeName,
    department: department,
    course_name: courseName,
    course_date: courseDate,
    course_duration: parseInt(courseDuration),
    training_type: trainingType,
    course_cost: parseFloat(courseCost),
    notes: notes
  };

  try {
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/training/${editId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(trainingData)
    });

    if (response.ok) {
      alert('تم تحديث الدورة التدريبية بنجاح');

      // إعادة تحميل البيانات في DataTables إذا كان مهيأ
      if (window.addedTrainingDataTable) {
        window.addedTrainingDataTable.ajax.reload();
      } else {
        await loadTrainingCourses();
      }

      closeEditModal();
    } else {
      const errorData = await response.json();
      alert(errorData.error || 'فشل في تحديث الدورة التدريبية');
    }
  } catch (error) {
    console.error('خطأ في تحديث الدورة التدريبية:', error);
    alert('حدث خطأ أثناء تحديث الدورة التدريبية');
  }
}

// حذف الدورة التدريبية
async function deleteTrainingCourse(id) {
  // فحص صلاحية الحذف
  if (!hasPermission('delete_training')) {
    alert('ليس لديك صلاحية لحذف الدورات التدريبية');
    return;
  }

  if (!confirm('هل أنت متأكد من حذف هذه الدورة التدريبية؟')) return;

  try {
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/training/${id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (response.ok) {
      alert('تم حذف الدورة التدريبية بنجاح');

      // إعادة تحميل البيانات في DataTables إذا كان مهيأ
      if (window.addedTrainingDataTable) {
        window.addedTrainingDataTable.ajax.reload();
      } else {
        await loadTrainingCourses();
      }
    } else {
      alert('فشل في حذف الدورة التدريبية');
    }
  } catch (error) {
    console.error('خطأ في حذف الدورة التدريبية:', error);
    alert('حدث خطأ أثناء حذف الدورة التدريبية');
  }
}

// دوال التحميل والإشعارات
function showNotification(message, type = 'info') {
  const notification = document.createElement('div');
  notification.className = `notification ${type}`;
  notification.textContent = message;
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 5px;
    color: white;
    font-weight: bold;
    z-index: 10000;
    max-width: 300px;
    word-wrap: break-word;
  `;

  switch(type) {
    case 'error':
      notification.style.backgroundColor = '#f44336';
      break;
    case 'success':
      notification.style.backgroundColor = '#4CAF50';
      break;
    case 'warning':
      notification.style.backgroundColor = '#ff9800';
      break;
    default:
      notification.style.backgroundColor = '#2196F3';
  }

  document.body.appendChild(notification);

  setTimeout(() => {
    if (notification.parentNode) {
      notification.parentNode.removeChild(notification);
    }
  }, 5000);
}

// البحث في الدورات التدريبية من السيرفر
async function searchTrainingFromServer(searchTerm = '') {
  try {
    const params = new URLSearchParams();

    if (searchTerm) {
      params.append('employee_name', searchTerm);
      params.append('course_name', searchTerm);
      params.append('training_type', searchTerm);
    }

    let url = `${API_URL}/training`;
    if (params.toString()) {
      url = `${API_URL}/training/search?${params.toString()}`;
    }

    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });

    if (!response.ok) {
      throw new Error('فشل في جلب بيانات الدورات التدريبية');
    }

    const filteredCourses = await response.json();
    trainingCourses = filteredCourses;
    displayTrainingCourses();

  } catch (error) {
    console.error('خطأ في البحث:', error);
    showNotification('خطأ في البحث عن الدورات التدريبية', 'error');
  }
}

// البحث في جدول الدورات التدريبية (استخدام البحث من السيرفر)
function searchInTrainingTable(searchTerm) {
  searchTrainingFromServer(searchTerm);
}

// تحميل الإدارات
function loadDepartments() {
  const departments = [...new Set(employees.map(emp => emp.department).filter(dept => dept))];
  const departmentSelect = document.getElementById('reportDepartment');
  
  if (departmentSelect) {
    departmentSelect.innerHTML = '<option value="">جميع الإدارات</option>';
    departments.forEach(dept => {
      const option = document.createElement('option');
      option.value = dept;
      option.textContent = dept;
      departmentSelect.appendChild(option);
    });
  }
}

// تنسيق التاريخ الميلادي
function formatDate(dateString) {
  if (typeof DateUtils !== 'undefined') {
    return DateUtils.formatDateFromDatabase(dateString);
  }
  return '';
}

// تحويل التاريخ لتنسيق input date
function formatDateForInput(dateString) {
  if (typeof DateUtils !== 'undefined') {
    return DateUtils.formatDateForInput(dateString);
  }
  return '';
}

// تصدير بيانات التدريب
function exportTrainingData() {
  if (filteredCourses.length === 0) {
    alert('لا توجد بيانات للتصدير');
    return;
  }

  const data = filteredCourses.map(course => ({
    'كود الموظف': course.employee_code,
    'اسم الموظف': course.employee_name,
    'الإدارة': course.department || '',
    'اسم الدورة': course.course_name,
    'تاريخ الدورة': formatDate(course.course_date),
    'مدة الدورة': course.course_duration + ' يوم',
    'نوع التدريب': course.training_type,
    'التكلفة': course.course_cost + ' جنيه',
    'ملاحظات': course.notes || ''
  }));

  const ws = XLSX.utils.json_to_sheet(data);
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, 'الدورات التدريبية');

  const fileName = `الدورات_التدريبية_${new Date().toISOString().split('T')[0]}.xlsx`;
  XLSX.writeFile(wb, fileName);
}

// إنشاء تقرير التدريب
async function generateTrainingReport() {
  const department = document.getElementById('reportDepartment').value;
  const trainingType = document.getElementById('reportTrainingType').value;
  const startDate = document.getElementById('reportStartDate').value;
  const endDate = document.getElementById('reportEndDate').value;

  let reportData = [...trainingCourses];

  // تطبيق الفلاتر
  if (department) {
    reportData = reportData.filter(course => course.department === department);
  }

  if (trainingType) {
    reportData = reportData.filter(course => course.training_type === trainingType);
  }

  if (startDate) {
    const filterStartDate = new Date(startDate);
    filterStartDate.setHours(0, 0, 0, 0);
    reportData = reportData.filter(course => {
      const courseDate = new Date(course.course_date);
      courseDate.setHours(0, 0, 0, 0);
      return courseDate >= filterStartDate;
    });
  }

  if (endDate) {
    const filterEndDate = new Date(endDate);
    filterEndDate.setHours(23, 59, 59, 999);
    reportData = reportData.filter(course => {
      const courseDate = new Date(course.course_date);
      courseDate.setHours(0, 0, 0, 0);
      return courseDate <= filterEndDate;
    });
  }

  // حساب الإحصائيات
  const totalCourses = reportData.length;
  const totalCost = reportData.reduce((sum, course) => sum + parseFloat(course.course_cost || 0), 0);
  const avgDurationValue = totalCourses > 0 ?
    (reportData.reduce((sum, course) => sum + parseInt(course.course_duration || 0), 0) / totalCourses) : 0;
  const avgDuration = avgDurationValue % 1 === 0 ? parseInt(avgDurationValue) : avgDurationValue.toFixed(1);

  // تحديث ملخص التقرير
  document.getElementById('totalCourses').textContent = totalCourses;
  document.getElementById('totalCost').textContent = (totalCost % 1 === 0 ? parseInt(totalCost) : totalCost.toFixed(2)) + ' جنيه';
  document.getElementById('avgDuration').textContent = avgDuration + ' يوم';

  // عرض بيانات التقرير
  displayReportData(reportData);
}

// عرض بيانات التقرير
function displayReportData(data) {
  const tableBody = document.querySelector('#report-table tbody');
  if (!tableBody) return;

  tableBody.innerHTML = '';

  data.forEach(course => {
    const row = document.createElement('tr');
    row.innerHTML = `
      <td>${course.employee_code}</td>
      <td>${course.employee_name}</td>
      <td>${course.department || ''}</td>
      <td>${course.course_name}</td>
      <td>${formatDate(course.course_date)}</td>
      <td>${course.course_duration} يوم</td>
      <td>${course.training_type}</td>
      <td>${course.course_cost} جنيه</td>
    `;
    tableBody.appendChild(row);
  });
}

// تصدير تقرير التدريب
function exportTrainingReport() {
  const reportTable = document.getElementById('report-table');
  const reportData = [];

  const rows = reportTable.querySelectorAll('tbody tr');
  rows.forEach(row => {
    const cells = row.querySelectorAll('td');
    if (cells.length > 0) {
      reportData.push({
        'كود الموظف': cells[0].textContent,
        'اسم الموظف': cells[1].textContent,
        'الإدارة': cells[2].textContent,
        'اسم الدورة': cells[3].textContent,
        'تاريخ الدورة': cells[4].textContent,
        'مدة الدورة': cells[5].textContent,
        'نوع التدريب': cells[6].textContent,
        'التكلفة': cells[7].textContent
      });
    }
  });

  if (reportData.length === 0) {
    alert('لا توجد بيانات في التقرير للتصدير');
    return;
  }

  // إضافة ملخص التقرير
  const summary = [
    { 'البيان': 'إجمالي الدورات', 'القيمة': document.getElementById('totalCourses').textContent },
    { 'البيان': 'إجمالي التكلفة', 'القيمة': document.getElementById('totalCost').textContent },
    { 'البيان': 'متوسط مدة الدورة', 'القيمة': document.getElementById('avgDuration').textContent },
    { 'البيان': '', 'القيمة': '' } // فاصل
  ];

  const ws1 = XLSX.utils.json_to_sheet(summary);
  const ws2 = XLSX.utils.json_to_sheet(reportData);

  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws1, 'ملخص التقرير');
  XLSX.utils.book_append_sheet(wb, ws2, 'تفاصيل التقرير');

  const fileName = `تقرير_التدريب_${new Date().toISOString().split('T')[0]}.xlsx`;
  XLSX.writeFile(wb, fileName);
}







// إضافة تهيئة DataTables إلى setupEventListeners الموجود
function setupTrainingEventListeners() {
  // استدعاء setupEventListeners الأصلي
  setupEventListeners();

  // تهيئة DataTables للدورات التدريبية
  try {
    initializeTrainingDataTables();
  } catch (error) {
    console.error('خطأ في تهيئة DataTables:', error);
  }

  // ربط أحداث الفلاتر المتقدمة
  try {
    bindTrainingFilterEvents();
    loadTrainingDepartments();
  } catch (error) {
    console.error('خطأ في ربط أحداث الفلاتر:', error);
  }
}

// تهيئة DataTables للدورات التدريبية
function initializeTrainingDataTables() {
  // التحقق من وجود التوكن
  const token = localStorage.getItem('token');
  if (!token) {
    console.error('لا يوجد توكن مصادقة - لا يمكن تهيئة DataTables');
    alert('يرجى تسجيل الدخول أولاً');
    window.location.href = 'login.html';
    return;
  }

  // التأكد من وجود jQuery و DataTables
  if (typeof $ === 'undefined') {
    console.error('jQuery غير محمل');
    setTimeout(initializeTrainingDataTables, 1000);
    return;
  }

  if (typeof $.fn.DataTable === 'undefined') {
    console.error('DataTables غير محمل');
    setTimeout(initializeTrainingDataTables, 1000);
    return;
  }

  // التحقق من وجود الجدول في DOM
  if (!document.getElementById('addedTrainingTable')) {
    console.error('جدول الدورات التدريبية غير موجود في DOM');
    return;
  }

  try {
    // تهيئة DataTables
    const table = $('#addedTrainingTable').DataTable({
      processing: true,
      serverSide: true,
      ajax: {
        url: `${API_URL}/training/datatables`,
        type: 'GET',
        beforeSend: function(xhr) {
          const token = localStorage.getItem('token');
          if (token) {
            xhr.setRequestHeader('Authorization', `Bearer ${token}`);
          }
        },
        error: function(xhr, error, code) {
          console.error('خطأ في تحميل البيانات:', error);

          if (xhr.status === 401 || xhr.status === 403) {
            if (typeof handleTokenExpired === 'function') {
              handleTokenExpired();
            } else {
              alert('انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.');
              window.location.href = 'login.html';
            }
          } else {
            alert('حدث خطأ في تحميل البيانات. يرجى المحاولة مرة أخرى.');
          }
        }
      },
      columns: [
        { data: 0, title: 'رقم السجل', width: '8%', className: 'text-center' },
        { data: 1, title: 'كود الموظف', width: '10%', className: 'text-center' },
        { data: 2, title: 'اسم الموظف', width: '15%' },
        { data: 3, title: 'الإدارة', width: '12%' },
        { data: 4, title: 'اسم الدورة', width: '20%' },
        { data: 5, title: 'تاريخ الدورة', width: '10%', className: 'text-center' },
        { data: 6, title: 'مدة الدورة', width: '8%', className: 'text-center' },
        { data: 7, title: 'نوع التدريب', width: '10%' },
        { data: 8, title: 'التكلفة', width: '8%', className: 'text-center' },
        { data: 9, title: 'الإجراءات', orderable: false, searchable: false, width: '9%', className: 'text-center' }
      ],
      language: {
        url: 'https://cdn.datatables.net/plug-ins/1.13.7/i18n/ar.json'
      },
      pageLength: 25,
      lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "الكل"]],
      order: [[0, 'desc']], // ترتيب حسب رقم السجل تنازلياً
      responsive: true,
      dom: 'Brtip', // إزالة f لحذف البحث
      buttons: [
        {
          extend: 'excel',
          text: 'تصدير إلى Excel',
          className: 'btn btn-success',
          exportOptions: {
            columns: [0, 1, 2, 3, 4, 5, 6, 7, 8] // استبعاد عمود الإجراءات
          }
        }
      ],
      drawCallback: function(settings) {
        // إعادة ربط أحداث الأزرار بعد كل رسم للجدول
        bindTrainingActionButtons();

        // تطبيق الصلاحيات على الأزرار
        applyTrainingPermissionsToButtons();
      },
      initComplete: function() {
        // ربط الأحداث مرة أخرى بعد التهيئة الكاملة
        bindTrainingActionButtons();
        applyTrainingPermissionsToButtons();

        // تحميل الإدارات في الفلاتر
        loadTrainingDepartments();
      }
    });

    // حفظ مرجع الجدول للاستخدام في أماكن أخرى
    window.addedTrainingDataTable = table;

  } catch (error) {
    console.error('خطأ في تهيئة DataTables:', error);
    alert('حدث خطأ في تهيئة الجدول. يرجى إعادة تحميل الصفحة.');
  }
}

// ربط أحداث الأزرار في جدول DataTables
function bindTrainingActionButtons() {
  // ربط أزرار التعديل باستخدام jQuery للعمل مع المحتوى الديناميكي
  $(document).off('click', '.edit-training-btn').on('click', '.edit-training-btn', function() {
    const trainingId = $(this).data('training-id');
    if (trainingId) {
      editTrainingCourse(trainingId);
    }
  });

  // ربط أزرار الحذف باستخدام jQuery للعمل مع المحتوى الديناميكي
  $(document).off('click', '.delete-training-btn').on('click', '.delete-training-btn', function() {
    const trainingId = $(this).data('training-id');
    if (trainingId) {
      deleteTrainingCourse(trainingId);
    }
  });
}

// تطبيق الصلاحيات على الأزرار
function applyTrainingPermissionsToButtons() {
  $('[data-permission]').each(function() {
    const permission = $(this).attr('data-permission');
    if (!hasPermission(permission)) {
      $(this).hide();
    }
  });
}





// دالة لإعادة تهيئة DataTables (مفيدة بعد تسجيل الدخول)
function reinitializeTrainingDataTables() {
  // تدمير DataTables الموجود إذا كان موجوداً
  if (window.addedTrainingDataTable) {
    try {
      window.addedTrainingDataTable.destroy();
      window.addedTrainingDataTable = null;
    } catch (error) {
      console.warn('خطأ في تدمير DataTables:', error);
    }
  }

  // إعادة تهيئة DataTables
  setTimeout(initializeTrainingDataTables, 500);
}

// جعل الدوال متاحة في النطاق العام للوصول إليها من DataTables
window.deleteTrainingCourse = deleteTrainingCourse;
window.editTrainingCourse = editTrainingCourse;
window.loadTrainingCourses = loadTrainingCourses;
window.hasPermission = hasPermission;
window.bindTrainingActionButtons = bindTrainingActionButtons;
window.applyTrainingPermissionsToButtons = applyTrainingPermissionsToButtons;

// دالة مساعدة لإعادة ربط الأحداث يدوياً (للاستخدام في وقت التشخيص)
window.rebindTrainingEvents = function() {
  console.log('إعادة ربط أحداث التدريب يدوياً...');
  bindTrainingActionButtons();
  applyTrainingPermissionsToButtons();
};

// ==================== فلاتر البحث المتقدمة ====================

// تطبيق الفلاتر المتقدمة
function applyTrainingAdvancedFilters() {
  if (!window.addedTrainingDataTable) {
    console.error('جدول الدورات التدريبية غير مهيأ');
    return;
  }

  const filters = {
    trainingId: document.getElementById('searchTrainingId')?.value || '',
    employeeCode: document.getElementById('searchTrainingEmployeeCode')?.value || '',
    employeeName: document.getElementById('searchTrainingEmployeeName')?.value || '',
    department: document.getElementById('searchTrainingDepartment')?.value || '',
    courseName: document.getElementById('searchTrainingCourseName')?.value || '',
    trainingType: document.getElementById('searchTrainingType')?.value || '',
    dateFrom: document.getElementById('searchTrainingDateFrom')?.value || '',
    dateTo: document.getElementById('searchTrainingDateTo')?.value || '',
    minCost: document.getElementById('searchTrainingMinCost')?.value || '',
    maxCost: document.getElementById('searchTrainingMaxCost')?.value || '',
    minDuration: document.getElementById('searchTrainingMinDuration')?.value || '',
    maxDuration: document.getElementById('searchTrainingMaxDuration')?.value || ''
  };

  console.log('تطبيق فلاتر التدريب:', filters);

  // مسح البحث السابق أولاً
  window.addedTrainingDataTable
    .search('')
    .columns().search('');

  // تطبيق البحث على الأعمدة المحددة
  window.addedTrainingDataTable
    .column(0).search(filters.trainingId)      // رقم السجل
    .column(1).search(filters.employeeCode)    // كود الموظف
    .column(2).search(filters.employeeName)    // اسم الموظف
    .column(3).search(filters.department)      // الإدارة
    .column(4).search(filters.courseName)      // اسم الدورة
    .column(7).search(filters.trainingType);   // نوع التدريب

  // للفلاتر المعقدة (التواريخ والأرقام)، نحتاج لمعالجة خاصة في الخادم
  if (filters.dateFrom || filters.dateTo || filters.minCost || filters.maxCost ||
      filters.minDuration || filters.maxDuration) {

    // إضافة معاملات إضافية للطلب
    const settings = window.addedTrainingDataTable.settings()[0];
    settings.ajax.data = function(d) {
      d.dateFrom = filters.dateFrom;
      d.dateTo = filters.dateTo;
      d.minCost = filters.minCost;
      d.maxCost = filters.maxCost;
      d.minDuration = filters.minDuration;
      d.maxDuration = filters.maxDuration;
      return d;
    };
  }

  // إعادة رسم الجدول
  window.addedTrainingDataTable.draw();
}

// مسح جميع الفلاتر
function clearTrainingAdvancedFilters() {
  // مسح قيم الحقول
  document.getElementById('searchTrainingId').value = '';
  document.getElementById('searchTrainingEmployeeCode').value = '';
  document.getElementById('searchTrainingEmployeeName').value = '';
  document.getElementById('searchTrainingDepartment').value = '';
  document.getElementById('searchTrainingCourseName').value = '';
  document.getElementById('searchTrainingType').value = '';
  document.getElementById('searchTrainingDateFrom').value = '';
  document.getElementById('searchTrainingDateTo').value = '';
  document.getElementById('searchTrainingMinCost').value = '';
  document.getElementById('searchTrainingMaxCost').value = '';
  document.getElementById('searchTrainingMinDuration').value = '';
  document.getElementById('searchTrainingMaxDuration').value = '';

  if (window.addedTrainingDataTable) {
    // مسح جميع البحوث والفلاتر
    window.addedTrainingDataTable
      .search('')
      .columns().search('');

    // إزالة معاملات البحث الإضافية
    const settings = window.addedTrainingDataTable.settings()[0];
    settings.ajax.data = function(d) {
      return d;
    };

    // إعادة رسم الجدول
    window.addedTrainingDataTable.draw();
  }

  console.log('تم مسح جميع فلاتر التدريب');
}

// تحميل الإدارات في فلتر الإدارات
async function loadTrainingDepartments() {
  const departmentSelect = document.getElementById('searchTrainingDepartment');
  if (!departmentSelect) return;

  // مسح الخيارات الحالية
  departmentSelect.innerHTML = '<option value="">جميع الإدارات</option>';

  try {
    // الحصول على الإدارات من قائمة الموظفين
    if (employees && employees.length > 0) {
      const departments = [...new Set(employees.map(emp => emp.department).filter(dept => dept))];
      departments.sort();

      departments.forEach(dept => {
        const option = document.createElement('option');
        option.value = dept;
        option.textContent = dept;
        departmentSelect.appendChild(option);
      });
    } else {
      // إذا لم تكن قائمة الموظفين محملة، حاول تحميلها
      await loadEmployees();
      if (employees && employees.length > 0) {
        const departments = [...new Set(employees.map(emp => emp.department).filter(dept => dept))];
        departments.sort();

        departments.forEach(dept => {
          const option = document.createElement('option');
          option.value = dept;
          option.textContent = dept;
          departmentSelect.appendChild(option);
        });
      }
    }
  } catch (error) {
    console.error('خطأ في تحميل الإدارات:', error);
  }
}

// ربط أحداث الفلاتر
function bindTrainingFilterEvents() {
  // ربط زر تطبيق الفلاتر
  const applyBtn = document.getElementById('applyTrainingFilters');
  if (applyBtn) {
    applyBtn.addEventListener('click', applyTrainingAdvancedFilters);
  }

  // ربط زر مسح الفلاتر
  const clearBtn = document.getElementById('clearTrainingFilters');
  if (clearBtn) {
    clearBtn.addEventListener('click', clearTrainingAdvancedFilters);
  }

  // ربط البحث التلقائي عند الضغط على Enter
  const filterInputs = [
    'searchTrainingId', 'searchTrainingEmployeeCode', 'searchTrainingEmployeeName',
    'searchTrainingCourseName', 'searchTrainingMinCost', 'searchTrainingMaxCost',
    'searchTrainingMinDuration', 'searchTrainingMaxDuration'
  ];

  filterInputs.forEach(inputId => {
    const input = document.getElementById(inputId);
    if (input) {
      input.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          applyTrainingAdvancedFilters();
        }
      });
    }
  });

  // ربط البحث التلقائي للقوائم المنسدلة
  const selectInputs = ['searchTrainingDepartment', 'searchTrainingType'];
  selectInputs.forEach(selectId => {
    const select = document.getElementById(selectId);
    if (select) {
      select.addEventListener('change', applyTrainingAdvancedFilters);
    }
  });

  // ربط البحث التلقائي لحقول التاريخ
  const dateInputs = ['searchTrainingDateFrom', 'searchTrainingDateTo'];
  dateInputs.forEach(inputId => {
    const input = document.getElementById(inputId);
    if (input) {
      input.addEventListener('change', applyTrainingAdvancedFilters);
    }
  });
}

// جعل الدوال متاحة في النطاق العام
window.applyTrainingAdvancedFilters = applyTrainingAdvancedFilters;
window.clearTrainingAdvancedFilters = clearTrainingAdvancedFilters;
window.loadTrainingDepartments = loadTrainingDepartments;
window.bindTrainingFilterEvents = bindTrainingFilterEvents;