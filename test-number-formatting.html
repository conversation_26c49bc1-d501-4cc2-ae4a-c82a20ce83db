<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>اختبار تنسيق الأرقام والتواريخ</title>
  <link rel="stylesheet" href="style.css">
  <link rel="stylesheet" href="vacations.css">
  <style>
    .test-container {
      max-width: 800px;
      margin: 20px auto;
      padding: 20px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .test-section {
      margin-bottom: 30px;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }
    
    .test-section h3 {
      color: #673ab7;
      margin-bottom: 15px;
    }
    
    .test-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      padding: 8px;
      background: #f9f9f9;
      border-radius: 4px;
    }
    
    .test-label {
      font-weight: bold;
    }
    
    .test-value {
      color: #333;
    }
    
    .date-input-test {
      margin: 10px 0;
    }
    
    .date-input-test input {
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      margin-right: 10px;
    }
    
    .date-helper {
      display: block;
      color: #666;
      font-size: 0.85em;
      margin-top: 4px;
      font-style: italic;
      background-color: rgba(103, 58, 183, 0.1);
      padding: 2px 6px;
      border-radius: 4px;
      border-left: 3px solid #673ab7;
    }
  </style>
</head>
<body>
  <div class="test-container">
    <h1>اختبار تنسيق الأرقام والتواريخ</h1>
    
    <div class="test-section">
      <h3>اختبار تنسيق الأرقام</h3>
      <div id="number-tests"></div>
    </div>
    
    <div class="test-section">
      <h3>اختبار تنسيق المبالغ المالية</h3>
      <div id="currency-tests"></div>
    </div>
    
    <div class="test-section">
      <h3>اختبار تنسيق التواريخ</h3>
      <div class="date-input-test">
        <label for="testDate">تاريخ الاختبار:</label>
        <input type="date" id="testDate" value="2024-01-15">
        <small class="date-helper">اختر تاريخ لاختبار التنسيق</small>
      </div>
      <div id="date-tests"></div>
    </div>
    
    <div class="test-section">
      <h3>اختبار دوال التنسيق المحدثة</h3>
      <button onclick="runTests()" style="background: #673ab7; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">تشغيل الاختبارات</button>
      <div id="function-tests" style="margin-top: 15px;"></div>
    </div>
  </div>

  <script src="shared-utils.js"></script>
  <script src="main.js"></script>
  <script src="config.js"></script>
  <script>
    // اختبار تنسيق الأرقام
    function testNumbers() {
      const testNumbers = [7, 7.0, 7.00, 21, 21.0, 21.00, 30, 30.0, 30.00, 7.5, 21.25, 30.75, 0, 0.0, 0.00];
      const container = document.getElementById('number-tests');
      
      testNumbers.forEach(num => {
        const div = document.createElement('div');
        div.className = 'test-item';
        
        const formatted = formatNumber ? formatNumber(num) : 'دالة غير متاحة';
        
        div.innerHTML = `
          <span class="test-label">الرقم الأصلي: ${num}</span>
          <span class="test-value">المنسق: ${formatted}</span>
        `;
        container.appendChild(div);
      });
    }
    
    // اختبار تنسيق المبالغ
    function testCurrency() {
      const testAmounts = [7, 7.0, 7.00, 100, 100.0, 100.00, 1500.50, 2000.75, 0, 0.0];
      const container = document.getElementById('currency-tests');
      
      testAmounts.forEach(amount => {
        const div = document.createElement('div');
        div.className = 'test-item';
        
        const formatted = formatCurrency ? formatCurrency(amount) : 'دالة غير متاحة';
        
        div.innerHTML = `
          <span class="test-label">المبلغ الأصلي: ${amount}</span>
          <span class="test-value">المنسق: ${formatted}</span>
        `;
        container.appendChild(div);
      });
    }
    
    // اختبار تنسيق التواريخ
    function testDates() {
      const testDates = ['2024-01-15', '2024-12-31', '2023-06-26'];
      const container = document.getElementById('date-tests');
      
      testDates.forEach(dateStr => {
        const div = document.createElement('div');
        div.className = 'test-item';
        
        const formatted = Utils && Utils.formatDate ? Utils.formatDate(dateStr) : 'دالة غير متاحة';
        
        div.innerHTML = `
          <span class="test-label">التاريخ الأصلي: ${dateStr}</span>
          <span class="test-value">المنسق: ${formatted}</span>
        `;
        container.appendChild(div);
      });
    }
    
    // تشغيل جميع الاختبارات
    function runTests() {
      const container = document.getElementById('function-tests');
      container.innerHTML = '';
      
      // اختبار الدوال المختلفة
      const tests = [
        { name: 'formatNumber(7)', result: typeof formatNumber !== 'undefined' ? formatNumber(7) : 'غير متاح' },
        { name: 'formatNumber(7.00)', result: typeof formatNumber !== 'undefined' ? formatNumber(7.00) : 'غير متاح' },
        { name: 'formatNumber(21.5)', result: typeof formatNumber !== 'undefined' ? formatNumber(21.5) : 'غير متاح' },
        { name: 'formatCurrency(100)', result: typeof formatCurrency !== 'undefined' ? formatCurrency(100) : 'غير متاح' },
        { name: 'formatCurrency(100.00)', result: typeof formatCurrency !== 'undefined' ? formatCurrency(100.00) : 'غير متاح' },
        { name: 'formatCurrency(150.75)', result: typeof formatCurrency !== 'undefined' ? formatCurrency(150.75) : 'غير متاح' },
        { name: 'Utils.formatNumber(30)', result: Utils && Utils.formatNumber ? Utils.formatNumber(30) : 'غير متاح' },
        { name: 'Utils.formatNumber(30.00)', result: Utils && Utils.formatNumber ? Utils.formatNumber(30.00) : 'غير متاح' }
      ];
      
      tests.forEach(test => {
        const div = document.createElement('div');
        div.className = 'test-item';
        div.innerHTML = `
          <span class="test-label">${test.name}</span>
          <span class="test-value">${test.result}</span>
        `;
        container.appendChild(div);
      });
    }
    
    // تشغيل الاختبارات عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
      testNumbers();
      testCurrency();
      testDates();
    });
  </script>
</body>
</html>
